/**
 * PDF Viewer App
 *
 * @format
 */

import React, { useEffect } from 'react';
import { StatusBar, useColorScheme, Alert } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import 'react-native-gesture-handler';
import { initOnnxRuntime } from './src/utils/onnxRuntime';

// Import navigation
import AppNavigator from './src/navigation/AppNavigator';

// Import contexts
import { PDFProvider } from './src/context/PDFContext';
import { SettingsProvider } from './src/context/SettingsContext';
import { ChatProvider } from './src/context/ChatContext';

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  // Initialize ONNX runtime at app startup
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('Initializing app...');
        await initOnnxRuntime();
        console.log('App initialization complete');
      } catch (error) {
        console.error('Failed to initialize app:', error);
        Alert.alert(
          'Initialization Error',
          'Failed to initialize the app. Please try restarting the app.'
        );
      }
    };

    initializeApp();
  }, []);

  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      <SettingsProvider>
        <PDFProvider>
          <ChatProvider>
            <AppNavigator />
          </ChatProvider>
        </PDFProvider>
      </SettingsProvider>
    </SafeAreaProvider>
  );
}

export default App;
