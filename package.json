{"name": "PDFViewer", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/hooks": "^100.1.0", "@react-native-documents/picker": "^10.1.1", "@react-navigation/native": "^7.1.3", "@react-navigation/stack": "^7.2.7", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "node-libs-react-native": "^1.2.1", "onnxruntime-react-native": "^1.21.0", "path-browserify": "^1.0.1", "pdf-lib": "^1.17.1", "process": "^0.11.10", "react": "19.0.0", "react-native": "0.78.2", "react-native-blob-util": "^0.21.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-gifted-chat": "^2.8.1", "react-native-mlkit-ocr": "^0.3.0", "react-native-pdf": "^6.7.7", "react-native-polyfill-globals": "^3.1.0", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-sqlite-storage": "^6.0.1", "react-native-vector-icons": "^10.2.0", "rn-fetch-blob": "^0.12.0", "stream-browserify": "^3.0.0", "tokenizers": "^0.13.3", "web-streams-polyfill": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.2", "@react-native/eslint-config": "0.78.2", "@react-native/metro-config": "0.78.2", "@react-native/typescript-config": "0.78.2", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}