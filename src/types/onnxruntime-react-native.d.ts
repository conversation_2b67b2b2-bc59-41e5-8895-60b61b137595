declare module 'onnxruntime-react-native' {
  export interface InferenceSessionOptions {
    executionProviders?: string[];
    graphOptimizationLevel?: string;
    enableCpuMemArena?: boolean;
    enableMemPattern?: boolean;
    executionMode?: string;
  }

  export class Tensor {
    constructor(type: string, data: any, dims: number[]);
    data: any;
    dims: number[];
    type: string;
  }

  export class InferenceSession {
    static create(modelPath: string, options?: any): Promise<InferenceSession>;
    run(feeds: Record<string, any>, outputNames?: string[]): Promise<Record<string, any>>;
  }
}
