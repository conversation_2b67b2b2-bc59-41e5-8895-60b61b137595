declare module 'tokenizers' {
  export class Encoding {
    getIds(): number[];
    getTokens(): string[];
    getAttentionMask(): number[];
    getTypeIds(): number[];
    getOffsets(): [number, number][];
    getSpecialTokensMask(): number[];
    getOverflowing(): Encoding[];
    getLength(): number;
  }

  export class Tokenizer {
    static fromFile(path: string): Promise<Tokenizer>;
    encode(text: string): Promise<Encoding>;
    decode(ids: number[], skipSpecialTokens?: boolean): Promise<string>;
    setPreTokenizer(preTokenizer: any): void;
    setDecoder(decoder: any): void;
    setModel(model: any): void;
    setPostProcessor(postProcessor: any): void;
  }
}
