import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { RootStackParamList } from './types';

// Import screens
import HomeScreen from '../pages/HomeScreen';
import DocumentListScreen from '../pages/DocumentListScreen';
import DocumentViewerScreen from '../pages/DocumentViewerScreen';
import SettingsScreen from '../pages/SettingsScreen';
import ChatScreen from '../pages/ChatScreen';
import SafeAreaWrapper from '../atoms/SafeAreaWrapper';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  return (
    <SafeAreaWrapper>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Home"
          screenOptions={{
            headerShown: false,
          }}
        >
          <Stack.Screen name="Home" component={HomeScreen} />
          <Stack.Screen name="DocumentList" component={DocumentListScreen} />
          <Stack.Screen name="DocumentViewer" component={DocumentViewerScreen} />
          <Stack.Screen name="Settings" component={SettingsScreen} />
          <Stack.Screen name="Chat" component={ChatScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaWrapper>
  );
};

export default AppNavigator;
