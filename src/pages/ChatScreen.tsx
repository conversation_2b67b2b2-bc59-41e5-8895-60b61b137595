import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/types';
import { colors, spacing } from '../theme/theme';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import { useChatContext, ChatMessage } from '../context/ChatContext';
import { usePDFContext } from '../context/PDFContext';
import ChatInput from '../components/ChatInput';
import ModelSetup from '../components/ModelSetup';
import IconButton from '../components/IconButton';

type ChatScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Chat'>;
type ChatScreenRouteProp = RouteProp<RootStackParamList, 'Chat'>;

const ChatScreen: React.FC = () => {
  const navigation = useNavigation<ChatScreenNavigationProp>();
  const route = useRoute<ChatScreenRouteProp>();
  const documentId = route.params?.documentId;

  const {
    currentConversation,
    conversations,
    isGenerating,
    isModelReady,
    sendMessage,
    createNewConversation,
    clearConversation,
    deleteConversation,
    setCurrentConversation,
  } = useChatContext();

  const { documents } = usePDFContext();
  const [showConversations, setShowConversations] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  // Initialize conversation if needed
  useEffect(() => {
    const initializeConversation = async () => {
      if (documentId) {
        // Find document-specific conversation or create one
        const docConversation = conversations.find(
          conv => conv.documentIds.includes(documentId)
        );

        if (docConversation) {
          setCurrentConversation(docConversation.id);
        } else {
          // Get document title
          const document = documents.find(doc => doc.id === documentId);
          const title = document
            ? `Chat about ${document.title}`
            : 'New Document Chat';

          await createNewConversation(title, [documentId]);
        }
      } else if (!currentConversation && conversations.length > 0) {
        // Set most recent conversation as current
        const mostRecent = conversations.sort(
          (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()
        )[0];

        setCurrentConversation(mostRecent.id);
      } else if (!currentConversation) {
        // Create a new conversation
        await createNewConversation('New Conversation');
      }
    };

    initializeConversation();
  }, [documentId, conversations, currentConversation]);

  // Get messages from current conversation
  const getMessages = useCallback((): ChatMessage[] => {
    if (!currentConversation) return [];
    // Return messages in chronological order (oldest first)
    return currentConversation.messages;
  }, [currentConversation]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (flatListRef.current && currentConversation && currentConversation.messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 100);
    }
  }, [currentConversation?.messages?.length]);

  // Handle sending a message
  const handleSendMessage = useCallback((text: string) => {
    sendMessage(text);
  }, [sendMessage]);

  // Handle clearing the current conversation
  const handleClearConversation = useCallback(() => {
    if (currentConversation) {
      clearConversation(currentConversation.id);
    }
  }, [currentConversation, clearConversation]);

  // Handle selecting a conversation
  const handleSelectConversation = useCallback((conversationId: string) => {
    setCurrentConversation(conversationId);
    setShowConversations(false);
  }, [setCurrentConversation]);

  // Handle creating a new conversation
  const handleNewConversation = useCallback(async () => {
    await createNewConversation();
    setShowConversations(false);
  }, [createNewConversation]);

  // Handle deleting a conversation
  const handleDeleteConversation = useCallback((conversationId: string) => {
    deleteConversation(conversationId);
  }, [deleteConversation]);

  // Render conversation list item
  const renderConversationItem = useCallback(({ item }: { item: any }) => {
    const isActive = currentConversation?.id === item.id;

    return (
      <TouchableOpacity
        style={[
          styles.conversationItem,
          isActive && styles.activeConversationItem,
        ]}
        onPress={() => handleSelectConversation(item.id)}
      >
        <View style={styles.conversationItemContent}>
          <Text
            style={[
              styles.conversationTitle,
              isActive && styles.activeConversationTitle,
            ]}
            numberOfLines={1}
          >
            {item.title}
          </Text>
          <Text
            style={styles.conversationDate}
            numberOfLines={1}
          >
            {new Date(item.updatedAt).toLocaleDateString()}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteConversation(item.id)}
        >
          <IconButton name="delete" size={20} color={colors.error} onPress={() => handleDeleteConversation(item.id)} />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  }, [currentConversation, handleSelectConversation, handleDeleteConversation]);

  // Render conversation list
  const renderConversationList = useCallback(() => {
    if (!showConversations) return null;

    return (
      <View style={styles.conversationsContainer}>
        <View style={styles.conversationsHeader}>
          <Text variant="h3">Conversations</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShowConversations(false)}
          >
            <IconButton name="close" size={24} color={colors.text} onPress={() => setShowConversations(false)} />
          </TouchableOpacity>
        </View>

        <FlatList
          data={conversations.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())}
          renderItem={renderConversationItem}
          keyExtractor={item => item.id}
          style={styles.conversationsList}
          ListEmptyComponent={
            <Text style={styles.emptyText}>No conversations yet</Text>
          }
          ListFooterComponent={
            <Button
              title="New Conversation"
              onPress={handleNewConversation}
              style={styles.newConversationButton}
            />
          }
        />
      </View>
    );
  }, [
    showConversations,
    conversations,
    renderConversationItem,
    handleNewConversation
  ]);

  // Render header
  const renderHeader = useCallback(() => {
    return (
      <View style={styles.header}>
        <IconButton name="arrow-back" size={24} color={colors.text} onPress={() => navigation.goBack()} />
        <TouchableOpacity
          style={styles.titleContainer}
          onPress={() => setShowConversations(!showConversations)}
        >
          <Text variant="h3" numberOfLines={1} style={styles.title}>
            {currentConversation?.title || 'Chat'}
          </Text>
          <IconButton
            name={showConversations ? "expand-less" : "expand-more"}
            size={24}
            color={colors.text}
            onPress={() => setShowConversations(!showConversations)}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.clearButton}
          onPress={handleClearConversation}
        >
          <IconButton name="refresh" size={24} color={colors.text} onPress={handleClearConversation} />
        </TouchableOpacity>
      </View>
    );
  }, [
    navigation,
    currentConversation,
    showConversations,
    handleClearConversation
  ]);

  // If model is not ready, show setup screen
  if (!isModelReady) {
    return <ModelSetup />;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {renderHeader()}
      {renderConversationList()}

      <View style={styles.chatContainer}>
        <FlatList
          data={getMessages()}
          renderItem={({ item }) => (
            <View style={[
              styles.messageBubble,
              item.user._id === 'user' ? styles.userMessage : styles.assistantMessage
            ]}>
              <Text style={styles.messageText}>{item.text}</Text>
              {item.citations && item.citations.length > 0 && (
                <View style={styles.citationsContainer}>
                  <Text variant="caption" style={styles.citationsTitle}>Sources:</Text>
                  {item.citations.map((citation: any, index: number) => (
                    <Text key={index} variant="caption" style={styles.citationText}>
                      [{index + 1}] {citation.documentTitle}
                    </Text>
                  ))}
                </View>
              )}
            </View>
          )}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.messagesList}
          ref={flatListRef}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        />

        <ChatInput
          onSend={handleSendMessage}
          isGenerating={isGenerating}
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.background,
  },
  backButton: {
    padding: spacing.xs,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    marginHorizontal: spacing.sm,
  },
  clearButton: {
    padding: spacing.xs,
  },
  chatContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  messagesList: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    flexGrow: 1, // Ensure content takes up available space
  },
  messageBubble: {
    padding: spacing.md,
    borderRadius: 16,
    maxWidth: '80%',
    marginVertical: spacing.xs,
  },
  userMessage: {
    backgroundColor: colors.primary,
    alignSelf: 'flex-end',
  },
  assistantMessage: {
    backgroundColor: colors.secondary,
    alignSelf: 'flex-start',
  },
  messageText: {
    color: colors.background,
  },
  citationsContainer: {
    marginTop: spacing.sm,
  },
  citationsTitle: {
    fontWeight: 'bold',
    color: colors.background,
    marginBottom: spacing.xs,
  },
  citationText: {
    color: colors.background,
    marginBottom: spacing.xs,
  },
  conversationsContainer: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    zIndex: 10,
    maxHeight: '50%',
  },
  conversationsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  closeButton: {
    padding: spacing.xs,
  },
  conversationsList: {
    maxHeight: 300,
  },
  conversationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  activeConversationItem: {
    backgroundColor: colors.primary + '20', // 20% opacity
  },
  conversationItemContent: {
    flex: 1,
  },
  conversationTitle: {
    fontWeight: 'bold',
  },
  activeConversationTitle: {
    color: colors.primary,
  },
  conversationDate: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  deleteButton: {
    padding: spacing.xs,
  },
  emptyText: {
    padding: spacing.md,
    textAlign: 'center',
    color: colors.textSecondary,
  },
  newConversationButton: {
    margin: spacing.md,
  },
});

export default ChatScreen;
