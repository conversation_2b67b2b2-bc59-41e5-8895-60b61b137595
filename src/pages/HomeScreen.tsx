import React, { useCallback, useEffect, useMemo } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/types';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import Card from '../molecules/Card';
import { colors, spacing, typography } from '../theme/theme';
import { usePDFContext } from '../context/PDFContext';
import { getFilenameFromPath } from '../utils/fileUtils';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

interface HomeScreenProps {
  navigation: HomeScreenNavigationProp;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { documents, isLoading, importDocument, refreshDocuments } = usePDFContext();

  // Refresh documents when screen is focused
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      refreshDocuments();
    });

    return unsubscribe;
  }, [navigation, refreshDocuments]);

  // Handle document import
  const handleImportDocument = async () => {
    const newDoc = await importDocument();
    if (newDoc) {
      // Navigate to the viewer if import was successful
    console.log("Navigate to the viewer if import was successful HomeScreen");
      navigation.navigate('DocumentViewer', { documentId: newDoc.id });
    }
  };

  // Get recent documents (up to 3)
  const getRecentDocuments = () => {
    // Sort by last opened date (most recent first)
    return [...documents]
      .filter(doc => doc.lastOpened)
      .sort((a, b) => {
        const dateA = a.lastOpened ? new Date(a.lastOpened).getTime() : 0;
        const dateB = b.lastOpened ? new Date(b.lastOpened).getTime() : 0;
        return dateB - dateA;
      })
      .slice(0, 3); // Take only the first 3
  };

  const recentDocs = useMemo(getRecentDocuments, [documents]);

  console.log("HomeScreen");
  const renderRecentDocument = useCallback((doc: typeof documents[0]) => {
    const displayTitle = doc.title || getFilenameFromPath(doc.path);
    const lastOpenedDate = doc.lastOpened
      ? new Date(doc.lastOpened).toLocaleDateString()
      : 'Never';

    return (
      <TouchableOpacity
        key={doc.id}
        style={styles.recentDocItem}
        onPress={() => navigation.navigate('DocumentViewer', { documentId: doc.id })}
      >
        <Text variant="h3" numberOfLines={1} style={styles.recentDocTitle}>
          {displayTitle}
        </Text>
        <Text variant="caption">
          Last opened: {lastOpenedDate}
        </Text>
      </TouchableOpacity>
    );
  }, [navigation, getFilenameFromPath]);

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text variant="h1" style={styles.title}>PDF Viewer</Text>
        <Text variant="body" style={styles.subtitle}>
          Welcome to the PDF Viewer app. Browse and view your PDF documents.
        </Text>

      <View style={styles.cardsContainer}>
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Text variant="h3">Recent Documents</Text>
            {documents.length > 0 && (
              <TouchableOpacity onPress={() => navigation.navigate('DocumentList')}>
                <Text variant="body" style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            )}
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          ) : recentDocs.length > 0 ? (
            <View style={styles.recentDocsContainer}>
              {recentDocs.map(renderRecentDocument)}
            </View>
          ) : (
            <Text variant="body" style={styles.cardText}>
              You have no recent documents.
            </Text>
          )}

          <Button
            title="Browse Files"
            onPress={() => navigation.navigate('DocumentList')}
            style={styles.button}
          />
        </Card>

        <Card variant="outlined" style={styles.card}>
          <Text variant="h3">Quick Actions</Text>
          <View style={styles.actionsContainer}>
            <Button
              title="Import Document"
              onPress={handleImportDocument}
              style={styles.button}
            />
            <Button
              title="Chat with AI"
              variant="secondary"
              onPress={() => navigation.navigate('Chat', {})}
              style={styles.button}
            />
            <Button
              title="Settings"
              variant="outline"
              onPress={() => navigation.navigate('Settings')}
              style={styles.button}
            />
          </View>
        </Card>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: spacing.lg,
  },
  title: {
    marginBottom: spacing.sm,
  },
  subtitle: {
    marginBottom: spacing.xl,
    color: colors.textSecondary,
  },
  cardsContainer: {
    gap: spacing.lg,
  },
  card: {
    marginBottom: spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  viewAllText: {
    color: colors.primary,
    fontSize: typography.fontSizes.sm,
  },
  cardText: {
    marginVertical: spacing.md,
  },
  loadingContainer: {
    padding: spacing.md,
    alignItems: 'center',
  },
  recentDocsContainer: {
    marginVertical: spacing.md,
  },
  recentDocItem: {
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  recentDocTitle: {
    fontSize: typography.fontSizes.md,
    marginBottom: spacing.xs,
  },
  button: {
    marginTop: spacing.sm,
  },
  actionsContainer: {
    marginTop: spacing.md,
    gap: spacing.md,
  },
});

export default React.memo(HomeScreen);
