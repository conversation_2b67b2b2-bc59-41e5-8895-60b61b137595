import React from 'react';
import { View, StyleSheet, Switch, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/types';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import SafeAreaWrapper from '../atoms/SafeAreaWrapper';
import Card from '../molecules/Card';
import ModelManagement from '../components/ModelManagement';
import { colors, spacing } from '../theme/theme';
import { usePDFContext } from '../context/PDFContext';
import { useSettings } from '../context/SettingsContext';

type SettingsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Settings'>;

interface SettingsScreenProps {
  navigation: SettingsScreenNavigationProp;
}

const SettingsScreen: React.FC<SettingsScreenProps> = ({ navigation }) => {
  const { refreshDocuments } = usePDFContext();
  const { settings, updateSetting, resetSettings } = useSettings();

  // Clear all app data
  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will remove all documents and reset all settings. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: async () => {
            try {
              // Reset settings
              await resetSettings();

              // Refresh documents (this will trigger a sync)
              refreshDocuments();

              Alert.alert('Success', 'All data has been cleared');
            } catch (error) {
              console.error('Error clearing data:', error);
              Alert.alert('Error', 'Failed to clear data');
            }
          }
        },
      ]
    );
  };

  return (
    <SafeAreaWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text variant="h2">Settings</Text>
          <Button
            title="Back"
            variant="outline"
            size="small"
            onPress={() => navigation.goBack()}
          />
        </View>

        <ScrollView style={styles.content}>
          <Card style={styles.settingsCard}>
          <Text variant="h3" style={styles.sectionTitle}>Appearance</Text>

          <View style={styles.settingRow}>
            <Text variant="body">Dark Mode</Text>
            <Switch
              value={settings.appearance.darkMode}
              onValueChange={(value) => updateSetting('appearance', 'darkMode', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>
        </Card>

        <Card style={styles.settingsCard}>
          <Text variant="h3" style={styles.sectionTitle}>PDF Viewer Settings</Text>

          <View style={styles.settingRow}>
            <Text variant="body">Single Page Mode</Text>
            <Switch
              value={settings.pdfViewer.singlePageMode}
              onValueChange={(value) => updateSetting('pdfViewer', 'singlePageMode', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Show Page Numbers</Text>
            <Switch
              value={settings.pdfViewer.showPageNumbers}
              onValueChange={(value) => updateSetting('pdfViewer', 'showPageNumbers', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Fit to Width</Text>
            <Switch
              value={settings.pdfViewer.fitToWidth}
              onValueChange={(value) => updateSetting('pdfViewer', 'fitToWidth', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Enable Annotations</Text>
            <Switch
              value={settings.pdfViewer.enableAnnotations}
              onValueChange={(value) => updateSetting('pdfViewer', 'enableAnnotations', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>
        </Card>

        <Card style={styles.settingsCard}>
          <Text variant="h3" style={styles.sectionTitle}>Document Settings</Text>

          <View style={styles.settingRow}>
            <Text variant="body">Auto-save Changes</Text>
            <Switch
              value={settings.documents.autoSave}
              onValueChange={(value) => updateSetting('documents', 'autoSave', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Notifications</Text>
            <Switch
              value={settings.documents.notifications}
              onValueChange={(value) => updateSetting('documents', 'notifications', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Show Metadata</Text>
            <Switch
              value={settings.documents.showMetadata}
              onValueChange={(value) => updateSetting('documents', 'showMetadata', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Sort Documents By</Text>
            <View style={styles.sortOptions}>
              <TouchableOpacity
                style={[styles.sortOption, settings.documents.sortBy === 'lastOpened' && styles.selectedSortOption]}
                onPress={() => updateSetting('documents', 'sortBy', 'lastOpened')}
              >
                <Text variant="caption" style={settings.documents.sortBy === 'lastOpened' ? styles.selectedSortText : {}}>Recent</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, settings.documents.sortBy === 'name' && styles.selectedSortOption]}
                onPress={() => updateSetting('documents', 'sortBy', 'name')}
              >
                <Text variant="caption" style={settings.documents.sortBy === 'name' ? styles.selectedSortText : {}}>Name</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, settings.documents.sortBy === 'date' && styles.selectedSortOption]}
                onPress={() => updateSetting('documents', 'sortBy', 'date')}
              >
                <Text variant="caption" style={settings.documents.sortBy === 'date' ? styles.selectedSortText : {}}>Date</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Card>

        <Card style={styles.settingsCard}>
          <Text variant="h3" style={styles.sectionTitle}>AI Settings</Text>

          <View style={styles.settingRow}>
            <Text variant="body">Auto-download Models</Text>
            <Switch
              value={settings.ai.autoDownloadModels}
              onValueChange={(value) => updateSetting('ai', 'autoDownloadModels', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Use High-Quality Model</Text>
            <Switch
              value={settings.ai.useHighQualityModel}
              onValueChange={(value) => updateSetting('ai', 'useHighQualityModel', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>

          <View style={styles.settingRow}>
            <Text variant="body">Show Download Prompts</Text>
            <Switch
              value={settings.ai.showDownloadPrompts}
              onValueChange={(value) => updateSetting('ai', 'showDownloadPrompts', value)}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          </View>
        </Card>

        <Card style={styles.settingsCard}>
          <Text variant="h3" style={styles.sectionTitle}>AI Models</Text>
          <ModelManagement />
        </Card>

        <Card style={styles.settingsCard}>
          <Text variant="h3" style={styles.sectionTitle}>Data Management</Text>
          <Button
            title="Clear All Data"
            variant="outline"
            onPress={handleClearData}
            style={styles.dangerButton}
          />
        </Card>

        <Card style={styles.settingsCard}>
          <Text variant="h3" style={styles.sectionTitle}>About</Text>
          <Text variant="body" style={styles.aboutText}>
            PDF Viewer App v1.0.0
          </Text>
          <Text variant="caption">
            © 2023 PDF Viewer Inc.
          </Text>
        </Card>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  content: {
    padding: spacing.lg,
  },
  settingsCard: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    marginBottom: spacing.md,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  sortOptions: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    overflow: 'hidden',
  },
  sortOption: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.background,
  },
  selectedSortOption: {
    backgroundColor: colors.primary,
  },
  selectedSortText: {
    color: colors.background,
  },
  dangerButton: {
    borderColor: colors.error,
    marginTop: spacing.sm,
  },
  aboutText: {
    marginBottom: spacing.sm,
  },
});

export default SettingsScreen;
