import React, { useEffect } from 'react';
import { View, StyleSheet, FlatList, ActivityIndicator, Alert, TouchableOpacity } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/types';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import Card from '../molecules/Card';
import { colors, spacing } from '../theme/theme';
import { usePDFContext } from '../context/PDFContext';
import { DocumentMetadata } from '../utils/documentStorage';
import { getFilenameFromPath } from '../utils/fileUtils';

type DocumentListScreenNavigationProp = StackNavigationProp<RootStackParamList, 'DocumentList'>;

interface DocumentListScreenProps {
  navigation: DocumentListScreenNavigationProp;
}

const DocumentListScreen: React.FC<DocumentListScreenProps> = ({ navigation }) => {
  const { documents, isLoading, importDocument, deleteDocument, refreshDocuments } = usePDFContext();

  // Refresh documents when screen is focused
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      refreshDocuments();
    });

    return unsubscribe;
  }, [navigation, refreshDocuments]);

  // Handle document import
  const handleImportDocument = async () => {
    const newDoc = await importDocument();
    if (newDoc) {
      // Navigate to the viewer if import was successful
      console.log("Navigate to the viewer if import was successful");
      
      navigation.navigate('DocumentViewer', { documentId: newDoc.id });
    }
  };

  // Handle document deletion
  const handleDeleteDocument = async (documentId: string) => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteDocument(documentId);
            if (success) {
              // Show success message
              Alert.alert('Success', 'Document deleted successfully');
            }
          }
        },
      ]
    );
  };

  // Handle document opening
  const handleOpenDocument = async (documentId: string) => {
    console.log(" Handle document opening");
    navigation.navigate('DocumentViewer', { documentId });
  };

  // Render a document item
  const renderDocumentItem = ({ item }: { item: DocumentMetadata }) => {
    const displayTitle = item.title || getFilenameFromPath(item.path);
    const lastOpenedDate = item.lastOpened
      ? new Date(item.lastOpened).toLocaleDateString()
      : 'Never';

    return (
      <Card variant="outlined" style={styles.documentCard}>
        <TouchableOpacity
          style={styles.documentInfo}
          onPress={() => handleOpenDocument(item.id)}
        >
          <Text variant="h3" numberOfLines={1}>{displayTitle}</Text>
          <Text variant="caption">Pages: {item.pageCount || 'Unknown'}</Text>
          <Text variant="caption">Last opened: {lastOpenedDate}</Text>
        </TouchableOpacity>
        <View style={styles.documentActions}>
          <Button
            title="Open"
            size="small"
            onPress={() => handleOpenDocument(item.id)}
            style={styles.actionButton}
          />
          <Button
            title="Delete"
            variant="outline"
            size="small"
            onPress={() => handleDeleteDocument(item.id)}
            style={styles.actionButton}
          />
        </View>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text variant="h2">Documents</Text>
        <View style={styles.headerButtons}>
        <Button
          title="Import"
          size="small"
          onPress={handleImportDocument}
          style={styles.headerButton}
        />
        <Button
          title="Back"
          variant="outline"
          size="small"
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        />
      </View>
    </View>

    {isLoading ? (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text variant="body" style={styles.loadingText}>Loading documents...</Text>
      </View>
    ) : documents.length > 0 ? (
      <FlatList
        data={documents}
        renderItem={renderDocumentItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        refreshing={isLoading}
        onRefresh={refreshDocuments}
      />
    ) : (
      <View style={styles.emptyContainer}>
        <Text variant="body">No documents found.</Text>
        <Button
          title="Import Document"
          style={styles.importButton}
          onPress={handleImportDocument}
        />
      </View>
    )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: spacing.sm,
  },
  listContent: {
    padding: spacing.md,
  },
  documentCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  documentInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  documentActions: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.xs,
  },
  actionButton: {
    minWidth: 80,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
  },
  importButton: {
    marginTop: spacing.lg,
  },
});

export default DocumentListScreen;
