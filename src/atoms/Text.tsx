import React from 'react';
import { Text as RNText, TextProps as RNTextProps, StyleSheet } from 'react-native';
import { colors, typography } from '../theme/theme';

interface TextProps extends RNTextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'caption';
  color?: string;
}

const Text: React.FC<TextProps> = ({
  variant = 'body',
  color = colors.text,
  style,
  children,
  ...rest
}) => {
  const getTextStyle = () => {
    switch (variant) {
      case 'h1':
        return styles.h1;
      case 'h2':
        return styles.h2;
      case 'h3':
        return styles.h3;
      case 'h4':
        return styles.h4;
      case 'body':
        return styles.body;
      case 'caption':
        return styles.caption;
      default:
        return styles.body;
    }
  };

  return (
    <RNText style={[getTextStyle(), { color }, style]} {...rest}>
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  h1: {
    fontSize: typography.fontSizes.xxxl,
    fontWeight: typography.fontWeights.bold,
  },
  h2: {
    fontSize: typography.fontSizes.xxl,
    fontWeight: typography.fontWeights.bold,
  },
  h3: {
    fontSize: typography.fontSizes.xl,
    fontWeight: typography.fontWeights.medium,
  },
  h4: {
    fontSize: typography.fontSizes.lg,
    fontWeight: typography.fontWeights.medium,
  },
  body: {
    fontSize: typography.fontSizes.md,
    fontWeight: typography.fontWeights.regular,
  },
  caption: {
    fontSize: typography.fontSizes.xs,
    fontWeight: typography.fontWeights.regular,
    color: colors.textSecondary,
  },
});

export default Text;
