import React, { ReactNode } from 'react';
import { StyleSheet, View, Platform, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors } from '../theme/theme';

interface SafeAreaWrapperProps {
  children: ReactNode;
  backgroundColor?: string;
  edges?: Array<'top' | 'right' | 'bottom' | 'left'>;
}

/**
 * A wrapper component that handles safe area insets and camera cutouts
 */
const SafeAreaWrapper: React.FC<SafeAreaWrapperProps> = ({
  children,
  backgroundColor = colors.background,
  edges = ['top', 'right', 'bottom', 'left'],
}) => {
  const insets = useSafeAreaInsets();
  
  // Calculate padding based on safe area insets and which edges to apply
  const padding = {
    paddingTop: edges.includes('top') ? Math.max(insets.top, 20) : 0,
    paddingRight: edges.includes('right') ? insets.right : 0,
    paddingBottom: edges.includes('bottom') ? insets.bottom : 0,
    paddingLeft: edges.includes('left') ? insets.left : 0,
  };

  return (
    <View style={[styles.container, { backgroundColor }, padding]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SafeAreaWrapper;
