import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// Settings storage key
const SETTINGS_STORAGE_KEY = 'pdf_viewer_settings';

// Settings interface
export interface AppSettings {
  appearance: {
    darkMode: boolean;
  };
  pdfViewer: {
    defaultZoom: number;
    singlePageMode: boolean;
    showPageNumbers: boolean;
    fitToWidth: boolean;
    enableAnnotations: boolean;
  };
  documents: {
    autoSave: boolean;
    notifications: boolean;
    showMetadata: boolean;
    sortBy: 'lastOpened' | 'name' | 'date';
  };
  ai: {
    autoDownloadModels: boolean;
    useHighQualityModel: boolean;
    showDownloadPrompts: boolean;
  };
}

// Default settings
export const defaultSettings: AppSettings = {
  appearance: {
    darkMode: false,
  },
  pdfViewer: {
    defaultZoom: 1.0,
    singlePageMode: false,
    showPageNumbers: true,
    fitToWidth: true,
    enableAnnotations: false,
  },
  documents: {
    autoSave: true,
    notifications: true,
    showMetadata: true,
    sortBy: 'lastOpened',
  },
  ai: {
    autoDownloadModels: false,
    useHighQualityModel: false,
    showDownloadPrompts: true,
  },
};

// Settings context type
interface SettingsContextType {
  settings: AppSettings;
  isLoading: boolean;
  updateSetting: <K extends keyof AppSettings, SK extends keyof AppSettings[K]>(
    category: K,
    key: SK,
    value: AppSettings[K][SK]
  ) => void;
  resetSettings: () => Promise<void>;
}

// Create context
const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

// Hook to use settings context
export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

// Settings provider props
interface SettingsProviderProps {
  children: ReactNode;
}

// Settings provider component
export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, []);

  // Load settings from storage
  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const storedSettings = await AsyncStorage.getItem(SETTINGS_STORAGE_KEY);

      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings);

        // Check if we need to migrate settings to add new properties
        const migratedSettings = migrateSettings(parsedSettings);

        // Save migrated settings if needed
        if (migratedSettings !== parsedSettings) {
          await AsyncStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(migratedSettings));
        }

        setSettings(migratedSettings);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Migrate settings to add new properties
  const migrateSettings = (oldSettings: any): AppSettings => {
    const newSettings = { ...defaultSettings };

    // Copy existing settings
    if (oldSettings.appearance) {
      newSettings.appearance = { ...newSettings.appearance, ...oldSettings.appearance };
    }

    if (oldSettings.pdfViewer) {
      newSettings.pdfViewer = { ...newSettings.pdfViewer, ...oldSettings.pdfViewer };
    }

    if (oldSettings.documents) {
      newSettings.documents = { ...newSettings.documents, ...oldSettings.documents };
    }

    // Add ai settings if they don't exist
    if (!oldSettings.ai) {
      console.log('Migrating settings to add AI settings');
    } else {
      newSettings.ai = { ...newSettings.ai, ...oldSettings.ai };
    }

    return newSettings;
  };

  // Save settings to storage
  const saveSettings = async (newSettings: AppSettings) => {
    try {
      await AsyncStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    }
  };

  // Update a specific setting
  const updateSetting = <K extends keyof AppSettings, SK extends keyof AppSettings[K]>(
    category: K,
    key: SK,
    value: AppSettings[K][SK]
  ) => {
    const newSettings = { ...settings };
    newSettings[category][key] = value;
    saveSettings(newSettings);
  };

  // Reset settings to defaults
  const resetSettings = async () => {
    try {
      await AsyncStorage.removeItem(SETTINGS_STORAGE_KEY);
      setSettings(defaultSettings);
    } catch (error) {
      console.error('Error resetting settings:', error);
      Alert.alert('Error', 'Failed to reset settings');
    }
  };

  // Context value
  const value: SettingsContextType = {
    settings,
    isLoading,
    updateSetting,
    resetSettings,
  };

  return <SettingsContext.Provider value={value}>{children}</SettingsContext.Provider>;
};
