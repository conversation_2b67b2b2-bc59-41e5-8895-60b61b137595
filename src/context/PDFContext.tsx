import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import * as DocumentPicker from '@react-native-documents/picker';
import { Alert } from 'react-native';
import {
  DocumentMetadata,
  addDocument,
  removeDocument,
  syncDocuments,
  updateLastOpened
} from '../utils/documentStorage';
import { copyFileToDocumentsDir, deleteFile } from '../utils/fileUtils';
import { copySamplePDFs } from '../utils/sampleDocuments';
import { processDocument, processDocumentInBackground } from '../utils/documentProcessor';
import { ExtractedTextContent } from '../utils/textExtraction';
import { searchSimilarChunks, initDatabase } from '../utils/vectorDatabase';

interface PDFContextType {
  documents: DocumentMetadata[];
  isLoading: boolean;
  importDocument: () => Promise<DocumentMetadata | null>;
  openDocument: (documentId: string) => Promise<DocumentMetadata | null>;
  deleteDocument: (documentId: string) => Promise<boolean>;
  refreshDocuments: () => Promise<void>;
  // Text processing and search functionality
  processDocumentText: (documentId: string) => Promise<ExtractedTextContent | null>;
  searchDocuments: (query: string, limit?: number) => Promise<Array<{
    documentId: string;
    chunkId: string;
    text: string;
    score: number;
  }>>;
  isProcessingText: boolean;
}

const PDFContext = createContext<PDFContextType | undefined>(undefined);

export const usePDFContext = () => {
  const context = useContext(PDFContext);
  if (!context) {
    throw new Error('usePDFContext must be used within a PDFProvider');
  }
  return context;
};

interface PDFProviderProps {
  children: ReactNode;
}

export const PDFProvider: React.FC<PDFProviderProps> = ({ children }) => {
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isProcessingText, setIsProcessingText] = useState<boolean>(false);

  // Load documents on mount
  useEffect(() => {
    loadDocuments();

    // Initialize the vector database
    initDatabase().catch(error => {
      console.error('Error initializing vector database:', error);
    });
  }, []);

  // Load documents from storage
  const loadDocuments = async () => {
    try {
      setIsLoading(true);

      // Copy sample PDFs if needed (first launch)
      await copySamplePDFs();

      // Sync documents with file system
      const docs = await syncDocuments();
      setDocuments(docs);

      // Process unprocessed documents in the background
      processUnprocessedDocumentsInBackground(docs);
    } catch (error) {
      console.error('Error loading documents:', error);
      Alert.alert('Error', 'Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  // Process unprocessed documents in the background
  const processUnprocessedDocumentsInBackground = (docs: DocumentMetadata[]) => {
    // Find documents that haven't been processed yet
    const unprocessedDocs = docs.filter(doc => !doc.isTextProcessed);

    if (unprocessedDocs.length === 0) {
      console.log('No unprocessed documents found');
      return;
    }

    console.log(`Processing ${unprocessedDocs.length} documents in the background`);

    // Process each document in the background
    unprocessedDocs.forEach(doc => {
      processDocumentInBackground(doc)
        .then(() => {
          // Refresh the documents list to get updated metadata
          refreshDocuments();
        })
        .catch(error => {
          console.error(`Error processing document ${doc.id} in background:`, error);
        });
    });
  };

  // Refresh documents and sync with file system
  const refreshDocuments = async () => {
    try {
      setIsLoading(true);
      const docs = await syncDocuments();
      setDocuments(docs);
    } catch (error) {
      console.error('Error refreshing documents:', error);
      Alert.alert('Error', 'Failed to refresh documents');
    } finally {
      setIsLoading(false);
    }
  };

  // Import a new document
  const importDocument = async (): Promise<DocumentMetadata | null> => {
    try {
      // Pick a PDF document
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf],
      });

      // Handle single or multiple selection
      const pickedDoc = Array.isArray(result) ? result[0] : result;

      if (!pickedDoc.uri || !pickedDoc.name) {
        throw new Error('Invalid document selected');
      }

      // Copy the file to our app's documents directory
      const filePath = await copyFileToDocumentsDir(
        pickedDoc.uri,
        pickedDoc.name
      );

      // Add document to storage
      const newDoc = await addDocument(filePath);

      // Update state
      setDocuments(prevDocs => [...prevDocs, newDoc]);

      // Process the document text in the background
      processDocumentInBackground(newDoc)
        .then(() => {
          // Refresh the documents list to get updated metadata
          refreshDocuments();
        })
        .catch(error => {
          console.error(`Error processing document ${newDoc.id} in background:`, error);
        });

      return newDoc;
    } catch (error: any) {
      // Check if user cancelled the picker
      if (error.code === 'DOCUMENT_PICKER_CANCELED') {
        console.log('Document picking cancelled');
        return null;
      }
      console.error('Error importing document:', error);
      Alert.alert('Error', 'Failed to import document');
      return null;
    }
  };

  // Open a document and update last opened timestamp
  const openDocument = useCallback(async (documentId: string): Promise<DocumentMetadata | null> => {
    try {
      const doc = documents.find(d => d.id === documentId);
      if (!doc) {
        return null;
      }

      // Update last opened timestamp
      await updateLastOpened(documentId);

      // Update document in state
      const updatedDoc = { ...doc, lastOpened: new Date().toISOString() };
      setDocuments(prevDocs =>
        prevDocs.map(d => d.id === documentId ? updatedDoc : d)
      );

      return updatedDoc;
    } catch (error) {
      console.error('Error opening document:', error);
      Alert.alert('Error', 'Failed to open document');
      return null;
    }
  },[]);

  // Delete a document
  const deleteDocument = async (documentId: string): Promise<boolean> => {
    try {
      const doc = documents.find(d => d.id === documentId);
      if (!doc) {
        return false;
      }

      // Delete the file
      await deleteFile(doc.path);

      // Remove from storage
      const success = await removeDocument(documentId);

      if (success) {
        // Update state
        setDocuments(prevDocs => prevDocs.filter(d => d.id !== documentId));
      }

      return success;
    } catch (error) {
      console.error('Error deleting document:', error);
      Alert.alert('Error', 'Failed to delete document');
      return false;
    }
  };

  // Process document text
  const processDocumentText = async (documentId: string): Promise<ExtractedTextContent | null> => {
    try {
      setIsProcessingText(true);

      const doc = documents.find(d => d.id === documentId);
      if (!doc) {
        return null;
      }

      // Process the document
      const result = await processDocument(doc);

      if (result.success && result.textContent) {
        // Update document in state
        setDocuments(prevDocs =>
          prevDocs.map(d => d.id === documentId ? result.document : d)
        );

        return result.textContent;
      }

      return null;
    } catch (error) {
      console.error('Error processing document text:', error);
      Alert.alert('Error', 'Failed to process document text');
      return null;
    } finally {
      setIsProcessingText(false);
    }
  };

  // Search documents
  const searchDocuments = async (
    query: string,
    limit: number = 5
  ): Promise<Array<{
    documentId: string;
    chunkId: string;
    text: string;
    score: number;
  }>> => {
    try {
      if (!query.trim()) {
        return [];
      }

      // Search for similar chunks
      const results = await searchSimilarChunks(query, limit);

      return results;
    } catch (error) {
      console.error('Error searching documents:', error);
      Alert.alert('Error', 'Failed to search documents');
      return [];
    }
  };

  const value: PDFContextType = {
    documents,
    isLoading,
    importDocument,
    openDocument,
    deleteDocument,
    refreshDocuments,
    processDocumentText,
    searchDocuments,
    isProcessingText,
  };

  return <PDFContext.Provider value={value}>{children}</PDFContext.Provider>;
};
