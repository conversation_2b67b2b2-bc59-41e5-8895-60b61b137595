import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { colors, spacing } from '../theme/theme';
import Text from '../atoms/Text';
import { ChatMessage as ChatMessageType } from '../context/ChatContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/types';

interface ChatMessageProps {
  message: ChatMessageType;
}

const ChatMessageComponent: React.FC<ChatMessageProps> = ({ message }) => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const citations = message.citations || [];

  // Handle citation click
  const handleCitationClick = (documentId: string) => {
    navigation.navigate('DocumentViewer', { documentId });
  };

  // Format message text with clickable citations
  const formatMessageWithCitations = () => {
    if (!message.text || citations.length === 0) {
      return message.text;
    }

    // Replace citation markers with clickable spans
    const parts = [];
    let lastIndex = 0;
    const citationRegex = /\[(\d+)\]/g;
    let match;

    while ((match = citationRegex.exec(message.text)) !== null) {
      // Add text before the citation
      if (match.index > lastIndex) {
        parts.push(
          <Text key={`text-${lastIndex}`} style={styles.messageText}>
            {message.text.substring(lastIndex, match.index)}
          </Text>
        );
      }

      // Add the citation
      const citationIndex = parseInt(match[1]) - 1;
      if (citationIndex >= 0 && citationIndex < citations.length) {
        const citation = citations[citationIndex];
        parts.push(
          <TouchableOpacity
            key={`citation-${match.index}`}
            onPress={() => handleCitationClick(citation.documentId)}
          >
            <Text style={styles.citationText}>{match[0]}</Text>
          </TouchableOpacity>
        );
      } else {
        parts.push(
          <Text key={`citation-${match.index}`} style={styles.messageText}>
            {match[0]}
          </Text>
        );
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < message.text.length) {
      parts.push(
        <Text key={`text-${lastIndex}`} style={styles.messageText}>
          {message.text.substring(lastIndex)}
        </Text>
      );
    }

    return <View style={styles.messageTextContainer}>{parts}</View>;
  };

  // Render custom message bubble
  const renderCustomBubble = () => {
    const isUser = message.user._id === 'user';

    return (
      <View style={[
        styles.bubble,
        isUser ? styles.userBubble : styles.assistantBubble
      ]}>
        {formatMessageWithCitations()}
      </View>
    );
  };

  // Render citations list
  const renderCitations = () => {
    if (!citations || citations.length === 0) {
      return null;
    }

    return (
      <View style={styles.citationsContainer}>
        <Text variant="caption" style={styles.citationsTitle}>Sources:</Text>
        {citations.map((citation, index) => (
          <TouchableOpacity
            key={`citation-list-${index}`}
            style={styles.citationItem}
            onPress={() => handleCitationClick(citation.documentId)}
          >
            <Text variant="caption" style={styles.citationItemText}>
              [{index + 1}] {citation.documentTitle}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderCustomBubble()}
      {renderCitations()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bubble: {
    padding: spacing.md,
    borderRadius: 16,
    maxWidth: '80%',
    marginBottom: spacing.xs,
  },
  userBubble: {
    backgroundColor: colors.primary,
    alignSelf: 'flex-end',
  },
  assistantBubble: {
    backgroundColor: colors.secondary,
    alignSelf: 'flex-start',
  },
  messageTextContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  messageText: {
    color: colors.background,
    fontSize: 16,
  },
  citationText: {
    color: colors.background,
    fontWeight: 'bold',
    textDecorationLine: 'underline',
  },
  citationsContainer: {
    marginTop: spacing.xs,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
  },
  citationsTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  citationItem: {
    marginBottom: spacing.xs,
  },
  citationItemText: {
    color: colors.primary,
    textDecorationLine: 'underline',
  },
});

export default ChatMessageComponent;
