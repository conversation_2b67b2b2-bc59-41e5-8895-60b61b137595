import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { colors, spacing } from '../theme/theme';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import Card from '../molecules/Card';
import { ModelInfo } from '../utils/onnxRuntime';
import {
  getModelMetadata,
  downloadModel,
  deleteModel,
  MAX_DOWNLOAD_RETRIES,
  verifyAndCleanupModel
} from '../utils/modelManager';

const ModelManagement: React.FC = () => {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [downloadProgress, setDownloadProgress] = useState<{[key: string]: number}>({});
  const [isDownloading, setIsDownloading] = useState(false);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      setIsLoading(true);
      const modelData = await getModelMetadata();
      setModels(modelData);
    } catch (error) {
      console.error('Error loading models:', error);
      Alert.alert('Error', 'Failed to load model information');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyModels = async () => {
    try {
      setIsLoading(true);
      let modelsVerified = 0;
      let modelsFixed = 0;

      // Get current models
      const modelData = await getModelMetadata();

      // Verify each downloaded model
      for (const model of modelData) {
        if (model.isDownloaded) {
          console.log(`Verifying model ${model.id}...`);
          const wasValid = await verifyAndCleanupModel(model.id);
          modelsVerified++;

          if (!wasValid) {
            console.log(`Model ${model.id} was invalid and has been cleaned up`);
            modelsFixed++;
          }
        }
      }

      // Refresh model list
      await loadModels();

      // Show results
      if (modelsVerified > 0) {
        if (modelsFixed > 0) {
          Alert.alert(
            'Model Verification Complete',
            `Verified ${modelsVerified} models. Fixed ${modelsFixed} invalid models.`
          );
        } else {
          Alert.alert(
            'Model Verification Complete',
            `All ${modelsVerified} models are valid.`
          );
        }
      } else {
        Alert.alert('Model Verification', 'No downloaded models to verify.');
      }
    } catch (error) {
      console.error('Error verifying models:', error);
      Alert.alert('Error', 'Failed to verify models');
    } finally {
      setIsLoading(false);
    }
  };

  const [currentDownloadId, setCurrentDownloadId] = useState<string | null>(null);
  const [downloadAttempt, setDownloadAttempt] = useState(0);

  const handleDownload = async (modelId: string) => {
    try {
      setIsDownloading(true);
      setCurrentDownloadId(modelId);
      setDownloadProgress(prev => ({ ...prev, [modelId]: 0 }));
      setDownloadAttempt(1);

      // Get model info for better error messages
      const models = await getModelMetadata();
      const model = models.find(m => m.id === modelId);
      if (!model) {
        throw new Error(`Model ${modelId} not found`);
      }

      // First, verify and clean up any existing invalid model files
      await verifyAndCleanupModel(modelId);

      // Show initial download message with more detailed information
      Alert.alert(
        'Downloading Model',
        `Starting download of ${model.name} (${(model.size / (1024 * 1024)).toFixed(1)} MB). \n\n` +
        'Important tips for successful download:\n' +
        '• Keep the app open during download\n' +
        '• Ensure you have a stable internet connection\n' +
        '• Make sure you have enough free storage space\n' +
        '• The download may take several minutes\n\n' +
        'The app will automatically retry if the download is interrupted.',
        [{ text: 'OK' }]
      );

      // Progress callback with retry information
      const progressHandler = (progress: number, attempt?: number) => {
        if (attempt && attempt > downloadAttempt) {
          setDownloadAttempt(attempt);
        }
        setDownloadProgress(prev => ({ ...prev, [modelId]: progress }));
      };

      await downloadModel(modelId, progressHandler);

      // Refresh model list
      await loadModels();
      Alert.alert('Success', `${model.name} downloaded successfully`);
    } catch (error: any) {
      console.error(`Error downloading model ${modelId}:`, error);

      // Format a more user-friendly error message
      let errorMessage = 'Failed to download model';

      if (error?.message) {
        if (error.message.includes('interrupted')) {
          errorMessage = 'Download was interrupted. This is often due to network instability or memory constraints.\n\n' +
                       'Tips to resolve this issue:\n' +
                       '• Connect to a stable Wi-Fi network\n' +
                       '• Close other apps to free up memory\n' +
                       '• Restart your device and try again\n' +
                       '• Try downloading at a different time';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Download timed out. This may be due to slow internet connection or server issues.';
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else if (error.message.includes('verification failed') || error.message.includes('invalid') || error.message.includes('Protobuf parsing failed')) {
          errorMessage = 'The downloaded model file appears to be corrupted or invalid. The app will automatically clean up the invalid file. Please try downloading again.';
        } else if (error.message.includes('Downloaded file not found')) {
          errorMessage = 'The app could not access the downloaded file. This may be due to storage permission issues. ' +
                         'Please check app permissions and ensure you have enough free storage space.';
        } else if (error.message.includes('after 3 attempts')) {
          errorMessage = 'Download failed after multiple attempts. Please try again later or check your internet connection.';
        } else {
          // Include the actual error message but clean it up
          errorMessage = `Download failed: ${error.message.replace(/Error:\s*/i, '')}`;
        }
      }

      Alert.alert('Download Error', errorMessage);
    } finally {
      setIsDownloading(false);
      setCurrentDownloadId(null);
      setDownloadProgress(prev => ({ ...prev, [modelId]: 0 }));
      setDownloadAttempt(0);
    }
  };

  const handleDelete = async (modelId: string) => {
    try {
      Alert.alert(
        'Confirm Deletion',
        'Are you sure you want to delete this model? You can download it again later.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: async () => {
              setIsLoading(true);
              await deleteModel(modelId);
              await loadModels();
              Alert.alert('Success', 'Model deleted successfully');
            },
          },
        ]
      );
    } catch (error) {
      console.error(`Error deleting model ${modelId}:`, error);
      Alert.alert('Error', 'Failed to delete model');
    } finally {
      setIsLoading(false);
    }
  };

  const formatSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading models...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text variant="h3" style={styles.title}>AI Models</Text>
      <Text style={styles.description}>
        Download and manage AI models for offline text generation and chat functionality.
      </Text>

      {models.map((model) => (
        <Card key={model.id} style={styles.modelCard}>
          <View style={styles.modelHeader}>
            <Text variant="h4">{model.name}</Text>
            <Text variant="caption" style={styles.modelType}>{model.type.toUpperCase()}</Text>
          </View>

          <Text style={styles.modelDescription}>{model.description}</Text>

          <View style={styles.modelDetails}>
            <Text variant="caption">Version: {model.version}</Text>
            <Text variant="caption">Size: {formatSize(model.size)}</Text>
            <Text variant="caption" style={model.isDownloaded ? styles.statusDownloaded : styles.statusNotDownloaded}>
              {model.isDownloaded ? 'Downloaded' : 'Not Downloaded'}
            </Text>
          </View>

          {downloadProgress[model.id] > 0 && downloadProgress[model.id] < 1 ? (
            <View style={styles.progressContainer}>
              <View style={styles.progressBarContainer}>
                <View
                  style={[
                    styles.progressBar,
                    { width: `${Math.round(downloadProgress[model.id] * 100)}%` }
                  ]}
                />
              </View>
              <View style={styles.progressTextContainer}>
                <Text style={styles.progressText}>
                  {Math.round(downloadProgress[model.id] * 100)}%
                </Text>
                {downloadAttempt > 1 && currentDownloadId === model.id && (
                  <Text style={styles.retryText}>Retry {downloadAttempt}/{MAX_DOWNLOAD_RETRIES}</Text>
                )}
              </View>
            </View>
          ) : (
            <View style={styles.modelActions}>
              {model.isDownloaded ? (
                <View style={styles.buttonRow}>
                  <Button
                    title="Re-download"
                    variant="outline"
                    size="small"
                    onPress={() => handleDownload(model.id)}
                    style={styles.redownloadButton}
                    disabled={isDownloading}
                  />
                  <Button
                    title="Delete"
                    variant="outline"
                    size="small"
                    onPress={() => handleDelete(model.id)}
                    style={styles.deleteButton}
                    disabled={isDownloading}
                  />
                </View>
              ) : (
                <Button
                  title="Download"
                  variant="primary"
                  size="small"
                  onPress={() => handleDownload(model.id)}
                  disabled={isDownloading}
                />
              )}
            </View>
          )}
        </Card>
      ))}

      <View style={styles.buttonContainer}>
        <Button
          title="Refresh"
          variant="outline"
          onPress={loadModels}
          style={styles.actionButton}
          disabled={isDownloading || isLoading}
        />
        <Button
          title="Verify Models"
          variant="outline"
          onPress={verifyModels}
          style={styles.actionButton}
          disabled={isDownloading || isLoading}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: spacing.sm,
  },
  loadingContainer: {
    padding: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  title: {
    marginBottom: spacing.sm,
  },
  description: {
    marginBottom: spacing.md,
    color: colors.textSecondary,
  },
  modelCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  modelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  modelType: {
    backgroundColor: colors.primary,
    color: colors.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: 4,
    overflow: 'hidden',
  },
  modelDescription: {
    marginBottom: spacing.sm,
    color: colors.textSecondary,
  },
  modelDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  statusDownloaded: {
    color: colors.success,
  },
  statusNotDownloaded: {
    color: colors.warning,
  },
  modelActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: spacing.sm,
  },
  deleteButton: {
    borderColor: colors.error,
  },
  redownloadButton: {
    borderColor: colors.warning,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.sm,
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  progressContainer: {
    marginTop: spacing.sm,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: spacing.xs,
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
  },
  progressTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressText: {
    textAlign: 'right',
    fontSize: 12,
    color: colors.textSecondary,
  },
  retryText: {
    fontSize: 12,
    color: colors.warning,
    fontWeight: 'bold',
  },
});

export default ModelManagement;
