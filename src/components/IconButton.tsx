import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import Text from '../atoms/Text';
import { colors, spacing } from '../theme/theme';

interface IconButtonProps {
  name: string;
  onPress: () => void;
  size?: number;
  color?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

// This is a simple replacement for react-native-vector-icons
// We're using text characters instead of actual icons
const IconButton: React.FC<IconButtonProps> = ({
  name,
  onPress,
  size = 24,
  color = colors.text,
  style,
  textStyle,
}) => {
  // Map icon names to text characters
  const getIconText = () => {
    switch (name) {
      case 'arrow-back':
        return '←';
      case 'close':
        return '✕';
      case 'expand-less':
        return '▲';
      case 'expand-more':
        return '▼';
      case 'refresh':
        return '↻';
      case 'delete':
        return '🗑';
      case 'send':
        return '➤';
      default:
        return '•';
    }
  };

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.icon,
          { fontSize: size, color },
          textStyle,
        ]}
      >
        {getIconText()}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: spacing.xs,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    textAlign: 'center',
  },
});

export default IconButton;
