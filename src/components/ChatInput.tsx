import React, { useState } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, ActivityIndicator, Text, Platform } from 'react-native';
import { colors, spacing } from '../theme/theme';

interface ChatInputProps {
  onSend: (text: string) => void;
  isGenerating: boolean;
  placeholder?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSend,
  isGenerating,
  placeholder = 'Type a message...',
}) => {
  const [text, setText] = useState('');

  const handleSend = () => {
    if (text.trim() && !isGenerating) {
      onSend(text.trim());
      setText('');
    }
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        value={text}
        onChangeText={setText}
        placeholder={placeholder}
        placeholderTextColor={colors.textSecondary}
        multiline
        maxLength={1000}
        editable={!isGenerating}
      />
      <TouchableOpacity
        style={[
          styles.sendButton,
          (!text.trim() || isGenerating) && styles.disabledButton,
        ]}
        onPress={handleSend}
        disabled={!text.trim() || isGenerating}
      >
        {isGenerating ? (
          <ActivityIndicator color={colors.background} size="small" />
        ) : (
          <Text style={{ color: colors.background, fontSize: 20 }}>➤</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
    backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingBottom: Platform.OS === 'ios' ? spacing.md : spacing.xxl, // Add extra padding for iOS
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 120,
    backgroundColor: colors.background,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    color: colors.text,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: colors.border,
  },
});

export default ChatInput;
