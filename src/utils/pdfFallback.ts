import RNFS from 'react-native-fs';
import { getFilenameFromPath } from './fileUtils';

/**
 * A fallback method to get basic PDF information without using pdf-lib
 * This is useful when pdf-lib fails to extract metadata
 */
export const getPDFBasicInfo = async (filePath: string): Promise<{
  title: string;
  pageCount: number;
  fileSize: number;
}> => {
  try {
    // Get file stats
    const fileStats = await RNFS.stat(filePath);
    
    // Get filename as title
    const title = getFilenameFromPath(filePath);
    
    return {
      title,
      pageCount: 1, // We can't determine page count without parsing the PDF
      fileSize: fileStats.size,
    };
  } catch (error) {
    console.error('Error getting basic PDF info:', error);
    return {
      title: getFilenameFromPath(filePath),
      pageCount: 1,
      fileSize: 0,
    };
  }
};

/**
 * Check if a file is a valid PDF by examining its header
 */
export const isValidPDF = async (filePath: string): Promise<boolean> => {
  try {
    // Read the first 5 bytes of the file
    const header = await RNFS.read(filePath, 5, 0, 'ascii');
    
    // Check if the file starts with the PDF signature '%PDF-'
    return header === '%PDF-';
  } catch (error) {
    console.error('Error checking PDF validity:', error);
    return false;
  }
};
