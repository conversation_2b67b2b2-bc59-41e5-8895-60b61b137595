import { searchSimilarChunks } from './vectorDatabase';
import { DocumentMetadata } from './documentStorage';
import { ContextWindow, addMessageToContext, generateText, GenerationParams } from './textGeneration';

// Search result with document metadata
export interface SearchResultWithMetadata {
  documentId: string;
  chunkId: string;
  text: string;
  score: number;
  document?: DocumentMetadata;
}

// Citation information
export interface Citation {
  documentId: string;
  documentTitle: string;
  chunkId: string;
  text: string;
  score: number;
}

// RAG result
export interface RAGResult {
  answer: string;
  citations: Citation[];
  sourceDocuments: DocumentMetadata[];
}

// Get documents by IDs
export const getDocumentsByIds = (
  documentIds: string[],
  documents: DocumentMetadata[]
): DocumentMetadata[] => {
  return documents.filter(doc => documentIds.includes(doc.id));
};

// Search documents and retrieve relevant chunks
export const searchDocumentsForQuery = async (
  query: string,
  documents: DocumentMetadata[],
  limit: number = 5
): Promise<SearchResultWithMetadata[]> => {
  try {
    // Search for similar chunks
    const results = await searchSimilarChunks(query, limit);

    // Add document metadata to results
    return results.map(result => {
      const document = documents.find(doc => doc.id === result.documentId);

      return {
        ...result,
        document,
      };
    });
  } catch (error) {
    console.error('Error searching documents for query:', error);
    return [];
  }
};

// Create RAG prompt
export const createRAGPrompt = (
  query: string,
  searchResults: SearchResultWithMetadata[]
): string => {
  let prompt = `I need information about the following query: "${query}"\n\n`;
  prompt += 'Here are some relevant passages from documents:\n\n';

  // Limit the number of results to avoid exceeding context window
  const maxResults = 5; // Limit to top 5 results
  const limitedResults = searchResults.slice(0, maxResults);

  // If we had to limit results, add a note
  if (limitedResults.length < searchResults.length) {
    prompt += `Note: Showing ${limitedResults.length} most relevant results out of ${searchResults.length} total.\n\n`;
  }

  // Add search results to prompt
  limitedResults.forEach((result, index) => {
    const documentTitle = result.document?.title || 'Unknown Document';

    // Limit the length of each result if it's very long
    let text = result.text;
    const maxTextLength = 500; // Limit each chunk to 500 characters

    if (text.length > maxTextLength) {
      text = text.substring(0, maxTextLength) + "... (truncated)";
    }

    prompt += `[${index + 1}] From "${documentTitle}":\n${text}\n\n`;
  });

  prompt += `Based on the above information, please answer the query: "${query}"\n`;
  prompt += 'If the information is not in the provided passages, please say so. ';
  prompt += 'Include citations like [1], [2], etc. to indicate which passages you used for each part of your answer.';

  return prompt;
};

// Generate answer with RAG
export const generateRAGAnswer = async (
  context: ContextWindow,
  query: string,
  documents: DocumentMetadata[],
  params: GenerationParams = {},
  onToken?: (token: string) => void
): Promise<RAGResult> => {
  try {
    // Search for relevant chunks
    const searchResults = await searchDocumentsForQuery(query, documents);

    // Create RAG prompt
    const ragPrompt = searchResults.length > 0
      ? createRAGPrompt(query, searchResults)
      : `I need information about: ${query}`;

    // Add prompt to context
    await addMessageToContext(context, 'user', ragPrompt);

    // Generate answer
    const answer = await generateText(ragPrompt, params, onToken);

    // Extract citations if we have search results
    let citations: Citation[] = [];
    let sourceDocuments: DocumentMetadata[] = [];

    if (searchResults.length > 0) {
      // Try to extract citations from the answer
      citations = extractCitations(answer, searchResults);

      // If no citations were found but we have search results, create citations from the top results
      if (citations.length === 0) {
        citations = createCitationsFromTopResults(searchResults, Math.min(3, searchResults.length));
      }

      // Get unique source documents
      const sourceDocumentIds = [...new Set(citations.map(citation => citation.documentId))];
      sourceDocuments = getDocumentsByIds(sourceDocumentIds, documents);
    }

    return {
      answer,
      citations,
      sourceDocuments,
    };
  } catch (error) {
    console.error('Error generating RAG answer:', error);

    // Provide a fallback response in case of error
    const fallbackAnswer = `I'm sorry, I encountered an issue while processing your query about "${query}". ` +
      `Please try again or rephrase your question.`;

    // Call the token callback if provided
    if (onToken) {
      const words = fallbackAnswer.split(' ');
      for (const word of words) {
        onToken(word + ' ');
        // Add a small delay to simulate typing
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }

    return {
      answer: fallbackAnswer,
      citations: [],
      sourceDocuments: [],
    };
  }
};

// Extract citations from answer
export const extractCitations = (
  answer: string,
  searchResults: SearchResultWithMetadata[]
): Citation[] => {
  const citations: Citation[] = [];
  const citationRegex = /\[(\d+)\]/g;
  let match;

  while ((match = citationRegex.exec(answer)) !== null) {
    const index = parseInt(match[1]) - 1;

    if (index >= 0 && index < searchResults.length) {
      const result = searchResults[index];
      const document = result.document;

      if (document) {
        // Check if citation not already added
        if (!citations.some(c => c.chunkId === result.chunkId)) {
          citations.push({
            documentId: result.documentId,
            documentTitle: document.title,
            chunkId: result.chunkId,
            text: result.text,
            score: result.score,
          });
        }
      }
    }
  }

  return citations;
};

// Create citations from top search results
export const createCitationsFromTopResults = (
  searchResults: SearchResultWithMetadata[],
  count: number
): Citation[] => {
  return searchResults.slice(0, count).map(result => ({
    documentId: result.documentId,
    documentTitle: result.document?.title || 'Unknown Document',
    chunkId: result.chunkId,
    text: result.text,
    score: result.score
  }));
};

// Format answer with citations
export const formatAnswerWithCitations = (
  answer: string,
  citations: Citation[]
): string => {
  let formattedAnswer = answer;

  // Replace citation markers with links
  const citationRegex = /\[(\d+)\]/g;
  formattedAnswer = formattedAnswer.replace(citationRegex, (match, index) => {
    const citationIndex = parseInt(index) - 1;

    if (citationIndex >= 0 && citationIndex < citations.length) {
      const citation = citations[citationIndex];
      return `[${index}](${citation.documentId})`;
    }

    return match;
  });

  return formattedAnswer;
};
