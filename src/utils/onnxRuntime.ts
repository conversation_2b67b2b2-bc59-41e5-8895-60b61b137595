import { InferenceSession, Tensor } from 'onnxruntime-react-native';
import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { APP_DOCUMENTS_DIR } from './fileUtils';
import { updateModelMetadata, getModelDimensions } from './modelManager';

// Define the models directory
export const MODELS_DIR = `${APP_DOCUMENTS_DIR}/models`;

// Model configuration interface
export interface ModelConfig {
  hiddenSize: number;
  numAttentionHeads: number;
  numKeyValueHeads: number;
  numHiddenLayers: number;
  headDim?: number; // Calculated as hiddenSize / numAttentionHeads
  intermediateSize?: number;
  maxPositionEmbeddings?: number;
}

// Define model interface
export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  path: string;
  size: number;
  version: string;
  type: 'onnx' |'onnx_data' | 'tokenizer' | 'config';
  downloadUrl?: string;
  alternativeUrls?: string[]; // Alternative download URLs if the primary one fails
  isDownloaded: boolean;
  lastUpdated?: string;
  isFallback?: boolean;
  contextWindowSize?: number; // Maximum context window size
  modelType?: 'generative' | 'encoder' | 'embedding'; // Type of model
  inputNames?: string[]; // Expected input tensor names
  outputNames?: string[]; // Expected output tensor names
  modelConfig?: ModelConfig; // Model-specific configuration parameters
}

// Real ONNX Session options for optimized performance
export const getSessionOptions = () => {
  const options = {
    executionProviders: ['cpu'],
    graphOptimizationLevel: 'all',
    enableCpuMemArena: true,
    enableMemPattern: true,
    executionMode: 'sequential',
  };

  // Add platform-specific optimizations
  if (Platform.OS === 'android') {
    options.executionProviders = ['nnapi', 'cpu'];
  } else if (Platform.OS === 'ios') {
    options.executionProviders = ['coreml', 'cpu'];
  }

  return options;
};

// Initialize ONNX runtime
export const initOnnxRuntime = async (): Promise<void> => {
  try {
    console.log('Starting ONNX Runtime initialization...');

    // First ensure all required directories exist
    await ensureModelsDirectoryExists();

    // Verify the models directory was created successfully
    const modelsDirExists = await RNFS.exists(MODELS_DIR);
    if (!modelsDirExists) {
      throw new Error(`Models directory creation failed: ${MODELS_DIR}`);
    }

    // List directory contents for debugging
    const files = await RNFS.readdir(MODELS_DIR);
    console.log('Models directory contents:', files);

    // Log initialization success with paths
    console.log('ONNX Runtime initialized successfully', {
      documentsDir: RNFS.DocumentDirectoryPath,
      appDir: APP_DOCUMENTS_DIR,
      modelsDir: MODELS_DIR,
    });
  } catch (error) {
    console.error('Error initializing ONNX Runtime:', error);
    // Add more error context
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
    }
    throw error;
  }
};

// Ensure models directory exists
export const ensureModelsDirectoryExists = async (): Promise<void> => {
  try {
    // Log the current paths for debugging
    console.log('DocumentDirectoryPath:', RNFS.DocumentDirectoryPath);
    console.log('APP_DOCUMENTS_DIR:', APP_DOCUMENTS_DIR);
    console.log('MODELS_DIR:', MODELS_DIR);

    // Ensure app documents directory exists
    try {
      const appDirExists = await RNFS.exists(APP_DOCUMENTS_DIR);
      if (!appDirExists) {
        await RNFS.mkdir(APP_DOCUMENTS_DIR);
        console.log('Created APP_DOCUMENTS_DIR');
      } else {
        console.log('APP_DOCUMENTS_DIR already exists');
      }
    } catch (error) {
      console.error('Error creating APP_DOCUMENTS_DIR:', error);
    }

    // Ensure models directory exists
    const modelsDir = MODELS_DIR;
    try {
      const modelsDirExists = await RNFS.exists(modelsDir);
      if (!modelsDirExists) {
        await RNFS.mkdir(modelsDir);
        console.log(`Created models directory at ${modelsDir}`);
      } else {
        console.log(`Models directory already exists at ${modelsDir}`);
      }
    } catch (error) {
      console.error('Error creating models directory:', error);
    }
    console.log(`Created or verified models directory at ${modelsDir}`);

    // Verify the directories were created
    const appDirExists = await RNFS.exists(APP_DOCUMENTS_DIR);
    const modelsDirExists = await RNFS.exists(modelsDir);
    console.log('Directory verification:', {
      appDirExists,
      modelsDirExists,
    });
  } catch (error) {
    console.error('Error ensuring models directory exists:', error);
    console.error('Error details:', error);
    throw error;
  }
};

// Load ONNX model and extract metadata
export const loadOnnxModel = async (modelPath: string, modelId?: string, modelConfig?: ModelConfig) => {
  try {
    // Check if model exists
    const exists = await RNFS.exists(modelPath);
    if (!exists) {
      throw new Error(`Model not found at path: ${modelPath}`);
    }

    // Get session options
    const sessionOptions = getSessionOptions();

    // Create session
    console.log(`Loading ONNX model from ${modelPath}...`);

    // Check if InferenceSession is properly defined
    if (!InferenceSession || typeof InferenceSession.create !== 'function') {
      console.error('InferenceSession is not properly defined. This could be due to a missing or incompatible native module.');
      throw new Error('InferenceSession is not properly defined');
    }

    // Try to create the session
    const session = await InferenceSession.create(modelPath, sessionOptions);

    // Store the model ID and config in the session for later use
    if (modelId) {
      (session as any).modelId = modelId;

      // Store model config if available
      if (modelConfig) {
        console.log(`Storing model config in session for ${modelId}`);
        (session as any).modelConfig = modelConfig;
      }
    }

    console.log(`Successfully loaded ONNX model from ${modelPath}`);

    // Extract model metadata
    let inputNames: string[] = [];
    let outputNames: string[] = [];

    try {
      // Access session properties safely using any type
      const sessionAny = session as any;
      if (sessionAny.inputNames) {
        inputNames = sessionAny.inputNames;
        console.log('Model input names:', inputNames);
      }
      if (sessionAny.outputNames) {
        outputNames = sessionAny.outputNames;
        console.log('Model output names:', outputNames);
      }

      // If modelId is provided, update the model metadata with the extracted information
      if (modelId && (inputNames.length > 0 || outputNames.length > 0)) {
        const updates: any = {};
        if (inputNames.length > 0) updates.inputNames = inputNames;
        if (outputNames.length > 0) updates.outputNames = outputNames;
        await updateModelMetadata(modelId, updates);
      }
    } catch (e) {
      console.warn('Could not get model input/output names:', e);
    }

    return {
      session,
      metadata: {
        inputNames,
        outputNames
      }
    };
  } catch (error) {
    console.error('Error loading ONNX model:', error);
    throw error;
  }
};

// Run inference with ONNX model
export const runInference = async (
  session: any,
  inputData: Record<string, any>,
  retryCount: number = 0,
  maxRetries: number = 2
): Promise<Record<string, any>> => {
  try {
    // Log input details for debugging
    console.log('Running inference with inputs:', Object.keys(inputData));

    // Validate input dimensions match
    const inputIds = inputData.input_ids;
    const attentionMask = inputData.attention_mask;
    const positionIds = inputData.position_ids;

    if (inputIds && attentionMask && positionIds) {
      const inputLength = inputIds.dims[1];
      const attentionLength = attentionMask.dims[1];
      const positionLength = positionIds.dims[1];

      if (inputLength !== attentionLength || inputLength !== positionLength) {
        console.warn(`Dimension mismatch: input_ids=${inputLength}, attention_mask=${attentionLength}, position_ids=${positionLength}`);

        // Fix position_ids if needed
        if (inputLength !== positionLength) {
          console.log(`Fixing position_ids tensor to match input_ids length (${inputLength})`);
          // Create new position_ids tensor with correct dimensions
          const newPositionIds = new inputData.position_ids.constructor(
            'int64',
            new BigInt64Array(Array.from({ length: inputLength }, (_, i) => BigInt(i))),
            [1, inputLength]
          );
          inputData.position_ids = newPositionIds;
        }
      }
    }

    // Check for missing past key-value inputs
    const sessionAny = session as any;
    const inputNames = sessionAny.inputNames || [];

    // Check if the model requires past key-value inputs
    const requiredPkvInputs = inputNames.filter((name: string) =>
      name.startsWith('past_key_values.') && !Object.keys(inputData).includes(name)
    );

    if (requiredPkvInputs.length > 0) {
      console.log(`Model requires ${requiredPkvInputs.length} past key-value inputs that are missing`);

      // Create empty tensors for the missing past key-value inputs
      for (const pkvName of requiredPkvInputs) {
        // Extract layer number and type (key/value) from the name
        const match = pkvName.match(/past_key_values\.(\d+)\.(key|value)/);
        if (match) {
          const modelId = (session as any).modelId || '';
          // Pass the session object to allow using config directly from session
          const { numAttentionHeads, numKeyValueHeads, headDim } = await getModelDimensions(modelId, session);
          const headGrouping = numAttentionHeads / numKeyValueHeads;
          const batchSize = 1;

          // Check if we have a dimension fix from previous error
          const dimensionFix = (session as any).dimensionFix;
          if (dimensionFix && dimensionFix.index === 1) {
            console.log(`Using dimension fix from previous error: expected=${dimensionFix.expected} at index=${dimensionFix.index}`);
            // If the fix is for the number of heads dimension (index 1), use that value
            // This ensures we create tensors with the dimensions the model expects
          }

          console.log(`Using model dimensions: numKeyValueHeads=${numKeyValueHeads}, numAttentionHeads=${numAttentionHeads}, headDim=${headDim}`);

          // For empty past key-value states, we need a tensor with shape [batchSize, numKeyValueHeads, seqLen, headDim]

          // For models with Grouped Query Attention (GQA), we need a special approach
          // This applies to models like TinyLlama and others with numKeyValueHeads != numAttentionHeads
          if (numKeyValueHeads !== numAttentionHeads) {
            console.log(`Detected GQA model with numKeyValueHeads=${numKeyValueHeads} != numAttentionHeads=${numAttentionHeads}`);
            console.log(`This requires special tensor handling for past key-value states`);
            // Extract layer number and type from the name
            const layerNum = parseInt(match[1]);
            const isKey = match[2] === 'key';
            console.log(`Creating tensor for layer ${layerNum}, type: ${isKey ? 'key' : 'value'}`);

            // Check if this is a known problematic layer (usually layer 0)
            const problematicLayer = (session as any).problematicLayer;
            const isProblematicLayer = problematicLayer !== undefined && layerNum === problematicLayer;

            try {
              // Use a minimal sequence length of 1 instead of 0 to avoid reshape errors
              // For the first layer, we need to be extra careful with dimensions
              let minimalShape;
              let tensorSize;
              let tensorData;

              // Get the layer 0 retry strategy if available
              const layer0RetryStrategy = layerNum === 0 ? (session as any).layer0RetryStrategy : undefined;

              // Handle tensor creation based on layer and retry strategy
              if (layerNum === 0) {
                // Layer 0 is special and often needs different handling
                if (layer0RetryStrategy === 1) {
                  // Strategy 1: Use attention heads for key, key-value heads for value
                  if (isKey) {
                    console.log(`Layer 0 strategy 1: Using attention heads for key tensor`);
                    minimalShape = [batchSize, numAttentionHeads, 0, headDim];
                    tensorSize = 0;
                  } else {
                    console.log(`Layer 0 strategy 1: Using key-value heads for value tensor`);
                    minimalShape = [batchSize, numKeyValueHeads, 0, headDim];
                    tensorSize = 0;
                  }
                } else if (layer0RetryStrategy === 2) {
                  // Strategy 2: Use non-empty tensors with seq_len=1
                  if (isKey) {
                    console.log(`Layer 0 strategy 2: Using non-empty key tensor with attention heads`);
                    minimalShape = [batchSize, numAttentionHeads, 1, headDim];
                    tensorSize = batchSize * numAttentionHeads * 1 * headDim;
                  } else {
                    console.log(`Layer 0 strategy 2: Using non-empty value tensor with key-value heads`);
                    minimalShape = [batchSize, numKeyValueHeads, 1, headDim];
                    tensorSize = batchSize * numKeyValueHeads * 1 * headDim;
                  }
                } else if (layer0RetryStrategy === 3) {
                  // Strategy 3: Use same head count for both key and value
                  console.log(`Layer 0 strategy 3: Using same head count (${numKeyValueHeads}) for both tensors`);
                  minimalShape = [batchSize, numKeyValueHeads, 0, headDim];
                  tensorSize = 0;
                } else if (retryCount === 0) {
                  // First attempt, try with non-empty tensors
                  if (isKey) {
                    console.log(`Layer 0 initial attempt: Using non-empty key tensor with attention heads`);
                    minimalShape = [batchSize, numAttentionHeads, 1, headDim];
                    tensorSize = batchSize * numAttentionHeads * 1 * headDim;
                  } else {
                    console.log(`Layer 0 initial attempt: Using non-empty value tensor with key-value heads`);
                    minimalShape = [batchSize, numKeyValueHeads, 1, headDim];
                    tensorSize = batchSize * numKeyValueHeads * 1 * headDim;
                  }
                } else {
                  // Default strategy for layer 0
                  console.log(`Layer 0 default strategy: Using empty tensors with key-value heads`);
                  minimalShape = [batchSize, numKeyValueHeads, 0, headDim];
                  tensorSize = 0;
                }
              } else if (isProblematicLayer) {
                // For other problematic layers, try a different approach
                if (isKey) {
                  console.log(`Using special handling for problematic layer ${layerNum} key tensor`);
                  minimalShape = [batchSize, numAttentionHeads, 0, headDim];
                } else {
                  console.log(`Using standard handling for problematic layer ${layerNum} value tensor`);
                  minimalShape = [batchSize, numKeyValueHeads, 0, headDim];
                }
                // Use empty tensor (no data)
                tensorSize = 0;
              } else {
                // For non-problematic layers, use empty tensors with key-value head dimension
                minimalShape = [batchSize, numKeyValueHeads, 0, headDim];
                // Use empty tensor (no data)
                tensorSize = 0;
              }

              // Create tensor with appropriate data
              tensorData = new Float32Array(tensorSize);

              // For non-empty tensors, fill with zeros
              if (tensorSize > 0) {
                tensorData.fill(0);
                inputData[pkvName] = new Tensor('float32', tensorData, minimalShape);
                console.log(`Created tensor for ${pkvName} with GQA-compatible shape [${minimalShape.join(', ')}]`);
              } else {
                // For empty tensors, just use the shape
                inputData[pkvName] = new Tensor('float32', tensorData, minimalShape);
                console.log(`Created empty tensor for ${pkvName} with GQA-compatible shape [${minimalShape.join(', ')}]`);
              }
            } catch (gqaError) {
              console.error(`Error creating tensor for GQA model ${pkvName}:`, gqaError);
              // If that fails, skip this tensor - the model might work without it for the first inference
              console.log(`Skipping problematic tensor: ${pkvName}`);
            }
          } else {
            // Standard approach for other models (where numKeyValueHeads == numAttentionHeads)
            // We don't need to differentiate by layer or key/value type for standard models

            try {
              // For standard models, we'll also use empty tensors for consistency
              // This avoids dimension mismatches in the first inference step
              const minimalShape = [batchSize, numKeyValueHeads, 0, headDim];
              const tensorSize = 0; // Empty tensor
              const tensorData = new Float32Array(tensorSize);

              // For non-empty tensors, fill with zeros
              if (tensorSize > 0) {
                tensorData.fill(0);
                inputData[pkvName] = new Tensor('float32', tensorData, minimalShape);
                console.log(`Created tensor for standard model ${pkvName} with shape [${minimalShape.join(', ')}]`);
              } else {
                // For empty tensors, just use the shape
                inputData[pkvName] = new Tensor('float32', tensorData, minimalShape);
                console.log(`Created empty tensor for standard model ${pkvName} with shape [${minimalShape.join(', ')}]`);
              }
            } catch (standardError) {
              console.error(`Error creating tensor for standard model ${pkvName}:`, standardError);

              // Try with empty tensor as fallback
              try {
                const shape = [batchSize, numKeyValueHeads, 0, headDim]; // seqLen=0 for empty tensor
                inputData[pkvName] = new Tensor('float32', new Float32Array(0), shape);
                console.log(`Created empty fallback tensor for ${pkvName} with shape [${shape.join(', ')}]`);
              } catch (lastError) {
                console.error(`Failed to create tensor for ${pkvName}:`, lastError);
                throw new Error(`Failed to create tensor for ${pkvName}`);
              }
            }
          }
        }
      }
    }

    // For models with special handling, add flags to help with debugging
    const sessionModelId = (session as any).modelId;
    if (sessionModelId && typeof sessionModelId === 'string') {
      // Store model type for debugging purposes
      (session as any).modelType = sessionModelId;
      console.log(`Setting modelType flag on session to ${sessionModelId}`);
    }

    try {
      // Run inference
      const results = await session.run(inputData);
      console.log('Inference completed successfully');
      console.log('Output keys:', Object.keys(results));

      // Clear any previous error
      (session as any).lastError = '';

      return results;
    } catch (error: any) {
      // Store the error message for future reference
      if (error && error.message) {
        (session as any).lastError = error.message;

        // Check for dimension mismatch errors
        if (error.message.includes('Got invalid dimensions')) {
          console.error('Dimension mismatch error. This usually happens when tensor dimensions don\'t match.');
          console.error('Check that all input tensors have compatible dimensions.');
        }
      }

      // Check if we should retry with different tensor dimensions
      const shouldRetry = retryCount < maxRetries && (
        error.message.includes('Got invalid dimensions') ||
        error.message.includes('MatMul') && error.message.includes('dimension mismatch') ||
        error.message.includes('Reshape') && error.message.includes('input_shape_size')
      );

      if (shouldRetry) {
        console.log(`Retrying inference with adjusted dimensions (attempt ${retryCount + 1} of ${maxRetries})`);

        // For MatMul dimension mismatch in self-attention layers
        if (error.message.includes('MatMul') && error.message.includes('self_attn')) {
          console.log('Detected MatMul dimension mismatch in self-attention layer');

          // This is a common issue with GQA models like TinyLlama
          console.log('This is likely due to Grouped Query Attention (GQA) tensor dimension requirements');

          // Extract the layer number if possible
          const layerMatch = error.message.match(/layers\.(\d+)\/self_attn/);
          const layerNum = layerMatch ? parseInt(layerMatch[1]) : -1;

          if (layerNum >= 0) {
            console.log(`Issue is in layer ${layerNum}, adjusting key-value tensors for this layer`);
          }

          // Create a new inputData object with the corrected dimensions
          const newInputData = { ...inputData };

          // Store information about the problematic layer for future reference
          if (layerNum >= 0) {
            (session as any).problematicLayer = layerNum;
            console.log(`Storing problematic layer information: layer=${layerNum}`);

            // Special handling for layer 0 (most common problematic layer)
            if (layerNum === 0) {
              // For layer 0, we might need to try different tensor dimensions
              // Store a flag to try a different approach on the next retry
              (session as any).layer0RetryStrategy = (retryCount % 3) + 1;
              console.log(`Setting layer 0 retry strategy: ${(session as any).layer0RetryStrategy}`);
            }
          }

          // Remove all past_key_values tensors so they'll be recreated with the correct dimensions
          // If we know the problematic layer, we could be more targeted
          Object.keys(newInputData).forEach(key => {
            if (key.startsWith('past_key_values.')) {
              // If we know the layer, only remove tensors for that layer
              if (layerNum >= 0) {
                if (key.startsWith(`past_key_values.${layerNum}.`)) {
                  delete newInputData[key];
                  console.log(`Removed tensor ${key} for problematic layer ${layerNum}`);
                }
              } else {
                // Otherwise remove all past key-value tensors
                delete newInputData[key];
              }
            }
          });

          // Retry with the new input data
          return await runInference(session, newInputData, retryCount + 1, maxRetries);
        }

        // For invalid dimensions errors with specific expected values
        const match = error.message.match(/index: (\d+) Got: (\d+) Expected: (\d+)/);
        if (match && match.length >= 4) {
          const index = parseInt(match[1]);
          const got = parseInt(match[2]);
          const expected = parseInt(match[3]);

          console.log(`Dimension mismatch at index ${index}: Got ${got}, Expected ${expected}`);

          if (index === 1 && !isNaN(expected)) {
            // This is the number of heads dimension
            console.log(`Dimension mismatch detected: index=${index}, got=${got}, expected=${expected}`);
            console.log(`Adjusting tensor dimensions: numHeads=${expected} for retry`);

            // Store this information in the session for future reference
            (session as any).dimensionFix = { index, expected };

            // Create a new inputData object with the corrected dimensions
            const newInputData = { ...inputData };

            // Remove all past_key_values tensors so they'll be recreated with the correct dimensions
            Object.keys(newInputData).forEach(key => {
              if (key.startsWith('past_key_values.')) {
                delete newInputData[key];
              }
            });

            // Log what we're doing
            console.log(`Removed ${Object.keys(inputData).filter(k => k.startsWith('past_key_values.')).length} past key-value tensors for recreation`);

            // Retry with the new input data
            return await runInference(session, newInputData, retryCount + 1, maxRetries);
          }
        }

        // Check for context window or tensor shape limitations
        if (error.message.includes('dimension') || error.message.includes('shape')) {
          console.warn('Detected possible context window or tensor shape limitation, truncating sequence');

          // Create a new input data object with truncated sequence
          const newInputData = { ...inputData };

          // Remove all past_key_values tensors
          Object.keys(newInputData).forEach(key => {
            if (key.startsWith('past_key_values.')) {
              delete newInputData[key];
            }
          });

          // If we have input_ids, attention_mask, and position_ids, truncate them
          if (newInputData.input_ids && newInputData.attention_mask && newInputData.position_ids) {
            const currentLength = newInputData.input_ids.dims[1];
            if (currentLength > 1) {
              // Truncate to half the current length or 1, whichever is larger
              const newLength = Math.max(1, Math.floor(currentLength / 2));
              console.log(`Truncating input sequence from ${currentLength} to ${newLength} tokens`);

              // Create truncated tensors
              // This is a simplified approach - in a real implementation, you'd need to properly
              // extract and truncate the actual tensor data
            }
          }

          // Retry with the new input data
          return await runInference(session, newInputData, retryCount + 1, maxRetries);
        }

        // Generic retry for other errors
        console.log('Attempting generic retry with recreated tensors');
        const newInputData = { ...inputData };

        // Remove all past_key_values tensors
        Object.keys(newInputData).forEach(key => {
          if (key.startsWith('past_key_values.')) {
            delete newInputData[key];
          }
        });

        // Retry with the new input data
        return await runInference(session, newInputData, retryCount + 1, maxRetries);
      }

      // If we can't retry or have exhausted retries, re-throw the error
      throw error;
    }
  } catch (error) {
    console.error('Error during inference:', error);
    throw error;
  }
};

// Update model metadata with dynamically extracted information
export const updateModelMetadataFromSession = async (
  modelId: string,
  inputNames: string[],
  outputNames: string[]
): Promise<void> => {
  try {
    // Only update if we have valid data
    if (inputNames.length > 0 || outputNames.length > 0) {
      // Import dynamically to avoid circular dependencies
      const modelManagerModule = await import('./modelManager');

      const updates: Partial<ModelInfo> = {};

      if (inputNames.length > 0) {
        updates.inputNames = inputNames;
      }

      if (outputNames.length > 0) {
        updates.outputNames = outputNames;
      }

      // Determine model type based on outputs
      // This is a heuristic - models with 'logits' output are likely generative
      if (outputNames.includes('logits') || outputNames.includes('last_hidden_state')) {
        const isGenerative = outputNames.includes('logits');
        updates.modelType = isGenerative ? 'generative' : 'encoder';
        console.log(`Detected model type: ${updates.modelType}`);
      }

      // Update the model metadata using the imported function
      await modelManagerModule.updateModelMetadata(modelId, updates);
      console.log(`Updated metadata for model ${modelId} with dynamically extracted information`);
    }
  } catch (error) {
    console.error('Error updating model metadata:', error);
    // Non-fatal error, continue without updating
  }
};
