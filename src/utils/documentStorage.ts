import AsyncStorage from '@react-native-async-storage/async-storage';
import { extractPDFMetadata, getAllPDFFiles, getFilenameFromPath } from './fileUtils';
import { getPDFBasicInfo, isValidPDF } from './pdfFallback';

// Storage key for document metadata
const DOCUMENTS_STORAGE_KEY = 'pdf_viewer_documents';

// Document metadata interface
export interface DocumentMetadata {
  id: string;
  path: string;
  title: string;
  author: string;
  pageCount: number;
  fileSize: number;
  lastOpened: string | null;
  addedDate: string;
  thumbnail?: string; // Base64 encoded thumbnail
  // Text processing fields
  isTextProcessed?: boolean;
  textProcessingError?: string;
  lastTextProcessed?: string;
  hasTextLayer?: boolean;
}

// Save document metadata to AsyncStorage
export const saveDocumentMetadata = async (documents: DocumentMetadata[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(DOCUMENTS_STORAGE_KEY, JSON.stringify(documents));
  } catch (error) {
    console.error('Error saving document metadata:', error);
    throw error;
  }
};

// Get all document metadata from AsyncStorage
export const getAllDocumentMetadata = async (): Promise<DocumentMetadata[]> => {
  try {
    const data = await AsyncStorage.getItem(DOCUMENTS_STORAGE_KEY);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error getting document metadata:', error);
    return [];
  }
};

// Add a new document to storage
export const addDocument = async (filePath: string): Promise<DocumentMetadata> => {
  try {
    // Check if the file is a valid PDF
    const valid = await isValidPDF(filePath);
    if (!valid) {
      console.warn(`File is not a valid PDF: ${filePath}`);
    }

    let metadata;
    try {
      // Try to extract metadata from the PDF
      metadata = await extractPDFMetadata(filePath);
    } catch (metadataError) {
      console.error('Error extracting metadata, using fallback:', metadataError);
      // Use fallback method if metadata extraction fails
      const basicInfo = await getPDFBasicInfo(filePath);
      metadata = {
        title: basicInfo.title,
        author: 'Unknown',
        creator: 'Unknown',
        producer: 'Unknown',
        creationDate: null,
        modificationDate: null,
        pageCount: basicInfo.pageCount,
        fileSize: basicInfo.fileSize,
      };
    }

    // Create a new document entry
    const newDocument: DocumentMetadata = {
      id: `doc_${Date.now()}`,
      path: filePath,
      title: metadata.title || getFilenameFromPath(filePath),
      author: metadata.author || 'Unknown',
      pageCount: metadata.pageCount,
      fileSize: metadata.fileSize,
      lastOpened: null,
      addedDate: new Date().toISOString(),
      // Initialize text processing fields
      isTextProcessed: false,
      textProcessingError: undefined,
      lastTextProcessed: undefined,
      hasTextLayer: undefined,
    };

    // Get existing documents and add the new one
    const documents = await getAllDocumentMetadata();
    documents.push(newDocument);

    // Save updated documents list
    await saveDocumentMetadata(documents);

    return newDocument;
  } catch (error) {
    console.error('Error adding document:', error);
    throw error;
  }
};

// Update a document's metadata
export const updateDocument = async (documentId: string, updates: Partial<DocumentMetadata>): Promise<DocumentMetadata | null> => {
  try {
    const documents = await getAllDocumentMetadata();
    const index = documents.findIndex(doc => doc.id === documentId);

    if (index === -1) {
      return null;
    }

    // Update the document
    documents[index] = { ...documents[index], ...updates };

    // Save updated documents list
    await saveDocumentMetadata(documents);

    return documents[index];
  } catch (error) {
    console.error('Error updating document:', error);
    throw error;
  }
};

// Remove a document from storage
export const removeDocument = async (documentId: string): Promise<boolean> => {
  try {
    const documents = await getAllDocumentMetadata();
    const updatedDocuments = documents.filter(doc => doc.id !== documentId);

    // If no documents were removed, return false
    if (documents.length === updatedDocuments.length) {
      return false;
    }

    // Save updated documents list
    await saveDocumentMetadata(updatedDocuments);

    return true;
  } catch (error) {
    console.error('Error removing document:', error);
    throw error;
  }
};

// Update last opened timestamp for a document
export const updateLastOpened = async (documentId: string): Promise<void> => {
  try {
    await updateDocument(documentId, {
      lastOpened: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating last opened:', error);
    throw error;
  }
};

// Sync document metadata with actual files
export const syncDocuments = async (): Promise<DocumentMetadata[]> => {
  try {
    // Get all PDF files in the documents directory
    const filePaths = await getAllPDFFiles();

    // Get existing document metadata
    const existingDocuments = await getAllDocumentMetadata();

    // Filter out documents that no longer exist
    const validDocuments = existingDocuments.filter(doc =>
      filePaths.some(path => path === doc.path)
    );

    // Find new files that don't have metadata yet
    const newFilePaths = filePaths.filter(path =>
      !existingDocuments.some(doc => doc.path === path)
    );

    // Create metadata for new files
    const newDocuments: DocumentMetadata[] = [];
    for (const path of newFilePaths) {
      try {
        // Check if the file is a valid PDF
        const valid = await isValidPDF(path);
        if (!valid) {
          console.warn(`Skipping invalid PDF file: ${path}`);
          continue;
        }

        let metadata;
        try {
          // Try to extract metadata
          metadata = await extractPDFMetadata(path);
        } catch (metadataError) {
          console.error(`Error extracting metadata for ${path}, using fallback:`, metadataError);
          // Use fallback method
          const basicInfo = await getPDFBasicInfo(path);
          metadata = {
            title: basicInfo.title,
            author: 'Unknown',
            creator: 'Unknown',
            producer: 'Unknown',
            creationDate: null,
            modificationDate: null,
            pageCount: basicInfo.pageCount,
            fileSize: basicInfo.fileSize,
          };
        }

        newDocuments.push({
          id: `doc_${Date.now()}_${newDocuments.length}`,
          path,
          title: metadata.title || getFilenameFromPath(path),
          author: metadata.author || 'Unknown',
          pageCount: metadata.pageCount,
          fileSize: metadata.fileSize,
          lastOpened: null,
          addedDate: new Date().toISOString(),
          // Initialize text processing fields
          isTextProcessed: false,
          textProcessingError: undefined,
          lastTextProcessed: undefined,
          hasTextLayer: undefined,
        });
      } catch (error) {
        console.error(`Error processing new file ${path}:`, error);
      }
    }

    // Combine valid existing documents with new ones
    const allDocuments = [...validDocuments, ...newDocuments];

    // Save updated documents list
    await saveDocumentMetadata(allDocuments);

    return allDocuments;
  } catch (error) {
    console.error('Error syncing documents:', error);
    return [];
  }
};
