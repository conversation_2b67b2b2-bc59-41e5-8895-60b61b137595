import RNFS from 'react-native-fs';
import { <PERSON>uffer } from 'buffer';
import { PDFDocument } from 'pdf-lib';
import MlkitOcr from 'react-native-mlkit-ocr';
import { APP_DOCUMENTS_DIR } from './fileUtils';

/**
 * Interface for extracted text content
 */
export interface ExtractedTextContent {
  fullText: string;
  chunks: TextChunk[];
  pageTexts: string[];
  isProcessed: boolean;
  processingError?: string;
  lastUpdated: string;
}

/**
 * Interface for text chunks
 */
export interface TextChunk {
  id: string;
  text: string;
  pageNumber: number;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  metadata?: {
    [key: string]: any;
  };
}

/**
 * Check if a PDF has a text layer
 */
export const checkPDFHasTextLayer = async (filePath: string): Promise<boolean> => {
  try {
    // Read the file as base64
    const fileData = await RNFS.readFile(filePath, 'base64');
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(Buffer.from(fileData, 'base64'));
    
    // Extract text from the first page as a test
    // If this fails or returns empty, the PDF likely doesn't have a text layer
    const pages = pdfDoc.getPages();
    if (pages.length === 0) {
      return false;
    }
    
    // This is a simplified check - in a real implementation, you would use
    // a PDF text extraction library that can check for text content
    // For now, we'll assume if the PDF can be loaded, it has a text layer
    return true;
  } catch (error) {
    console.error('Error checking PDF text layer:', error);
    return false;
  }
};

/**
 * Extract text from a PDF using its text layer
 */
export const extractTextFromPDF = async (filePath: string): Promise<ExtractedTextContent> => {
  try {
    // Read the file as base64
    const fileData = await RNFS.readFile(filePath, 'base64');
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(Buffer.from(fileData, 'base64'));
    
    // Get all pages
    const pages = pdfDoc.getPages();
    
    // In a real implementation, you would use a proper PDF text extraction library
    // pdf-lib doesn't provide text extraction capabilities
    // This is a placeholder for the actual implementation
    
    // For now, we'll create a mock result
    const pageTexts: string[] = pages.map((_, index) => 
      `[Placeholder text for page ${index + 1}]`
    );
    
    // Create chunks (in a real implementation, these would be actual text chunks)
    const chunks: TextChunk[] = pageTexts.flatMap((text, pageIndex) => {
      return [{
        id: `chunk_${pageIndex}_1`,
        text,
        pageNumber: pageIndex + 1,
        position: {
          x: 0,
          y: 0,
          width: 100,
          height: 100
        }
      }];
    });
    
    return {
      fullText: pageTexts.join('\n\n'),
      chunks,
      pageTexts,
      isProcessed: true,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return {
      fullText: '',
      chunks: [],
      pageTexts: [],
      isProcessed: false,
      processingError: error instanceof Error ? error.message : 'Unknown error',
      lastUpdated: new Date().toISOString()
    };
  }
};

/**
 * Extract text from a PDF using OCR
 */
export const extractTextFromPDFUsingOCR = async (filePath: string): Promise<ExtractedTextContent> => {
  try {
    // Create a temporary directory for image extraction
    const tempDir = `${APP_DOCUMENTS_DIR}/temp_ocr_${Date.now()}`;
    await RNFS.mkdir(tempDir);
    
    // In a real implementation, you would:
    // 1. Convert each PDF page to an image
    // 2. Run OCR on each image
    // 3. Combine the results
    
    // For now, we'll use a simplified approach with a mock image
    // In a real implementation, you would extract images from the PDF
    const mockImagePath = `${tempDir}/mock_page.png`;
    
    // Create a mock image file for testing
    // In a real implementation, you would extract actual page images
    await RNFS.writeFile(mockImagePath, '', 'base64');
    
    try {
      // Run OCR on the image
      const ocrResult = await MlkitOcr.recognize(mockImagePath);
      
      // Process OCR results
      const pageTexts = [ocrResult.map(block => block.text).join(' ')];
      
      // Create chunks from OCR blocks
      const chunks: TextChunk[] = ocrResult.map((block, index) => ({
        id: `ocr_chunk_${index}`,
        text: block.text,
        pageNumber: 1, // In a real implementation, this would be the actual page number
        position: {
          x: block.frame.x,
          y: block.frame.y,
          width: block.frame.width,
          height: block.frame.height
        }
      }));
      
      // Clean up temporary files
      await RNFS.unlink(tempDir);
      
      return {
        fullText: pageTexts.join('\n\n'),
        chunks,
        pageTexts,
        isProcessed: true,
        lastUpdated: new Date().toISOString()
      };
    } catch (ocrError) {
      console.error('OCR processing error:', ocrError);
      
      // Clean up temporary files
      await RNFS.unlink(tempDir);
      
      throw ocrError;
    }
  } catch (error) {
    console.error('Error extracting text using OCR:', error);
    return {
      fullText: '',
      chunks: [],
      pageTexts: [],
      isProcessed: false,
      processingError: error instanceof Error ? error.message : 'Unknown error',
      lastUpdated: new Date().toISOString()
    };
  }
};

/**
 * Process text content into chunks for better handling
 */
export const chunkTextContent = (text: string, maxChunkSize: number = 1000): string[] => {
  if (!text) return [];
  
  // Split by paragraphs first
  const paragraphs = text.split(/\n\s*\n/);
  
  const chunks: string[] = [];
  let currentChunk = '';
  
  for (const paragraph of paragraphs) {
    // If adding this paragraph would exceed the max chunk size,
    // save the current chunk and start a new one
    if (currentChunk.length + paragraph.length > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk);
      currentChunk = '';
    }
    
    // If the paragraph itself is longer than the max chunk size,
    // split it into sentences
    if (paragraph.length > maxChunkSize) {
      const sentences = paragraph.split(/(?<=[.!?])\s+/);
      
      for (const sentence of sentences) {
        if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
          chunks.push(currentChunk);
          currentChunk = '';
        }
        
        // If the sentence itself is too long, split it arbitrarily
        if (sentence.length > maxChunkSize) {
          let remainingSentence = sentence;
          while (remainingSentence.length > 0) {
            const chunkText = remainingSentence.slice(0, maxChunkSize);
            chunks.push(chunkText);
            remainingSentence = remainingSentence.slice(maxChunkSize);
          }
        } else {
          currentChunk += (currentChunk ? ' ' : '') + sentence;
        }
      }
    } else {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
    }
  }
  
  // Add the last chunk if it's not empty
  if (currentChunk) {
    chunks.push(currentChunk);
  }
  
  return chunks;
};

/**
 * Extract text from a PDF using the appropriate method
 */
export const processDocumentText = async (filePath: string): Promise<ExtractedTextContent> => {
  try {
    // Check if the PDF has a text layer
    const hasTextLayer = await checkPDFHasTextLayer(filePath);
    
    // Extract text using the appropriate method
    let extractedContent: ExtractedTextContent;
    
    if (hasTextLayer) {
      // Extract text from the PDF's text layer
      extractedContent = await extractTextFromPDF(filePath);
    } else {
      // Use OCR to extract text from the PDF
      extractedContent = await extractTextFromPDFUsingOCR(filePath);
    }
    
    return extractedContent;
  } catch (error) {
    console.error('Error processing document text:', error);
    return {
      fullText: '',
      chunks: [],
      pageTexts: [],
      isProcessed: false,
      processingError: error instanceof Error ? error.message : 'Unknown error',
      lastUpdated: new Date().toISOString()
    };
  }
};
