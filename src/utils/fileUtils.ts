import RNFS from 'react-native-fs';
import { PDFDocument } from 'pdf-lib';
import { <PERSON><PERSON><PERSON> } from 'buffer';

// Define the app's document directory
export const APP_DOCUMENTS_DIR = `${RNFS.DocumentDirectoryPath}/PDFViewer`;

// Ensure the app's document directory exists
export const ensureDirectoryExists = async (): Promise<void> => {
  try {
    const exists = await RNFS.exists(APP_DOCUMENTS_DIR);
    if (!exists) {
      await RNFS.mkdir(APP_DOCUMENTS_DIR);
    }
  } catch (error) {
    console.error('Error ensuring directory exists:', error);
    throw error;
  }
};

// Copy a file to the app's document directory
export const copyFileToDocumentsDir = async (
  sourcePath: string,
  filename: string
): Promise<string> => {
  try {
    await ensureDirectoryExists();

    // Create a unique filename to avoid overwriting
    const timestamp = new Date().getTime();
    const uniqueFilename = `${timestamp}_${filename}`;
    const destinationPath = `${APP_DOCUMENTS_DIR}/${uniqueFilename}`;

    await RNFS.copyFile(sourcePath, destinationPath);
    return destinationPath;
  } catch (error) {
    console.error('Error copying file:', error);
    throw error;
  }
};

// Delete a file from the app's document directory
export const deleteFile = async (filePath: string): Promise<void> => {
  try {
    const exists = await RNFS.exists(filePath);
    if (exists) {
      await RNFS.unlink(filePath);
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
};

// Extract metadata from a PDF file
export const extractPDFMetadata = async (filePath: string): Promise<{
  title: string;
  author: string;
  creator: string;
  producer: string;
  creationDate: string | null;
  modificationDate: string | null;
  pageCount: number;
  fileSize: number;
}> => {
  try {
    // Get file stats for size and dates
    const fileStats = await RNFS.stat(filePath);

    // Read the file as base64
    const fileData = await RNFS.readFile(filePath, 'base64');

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(Buffer.from(fileData, 'base64'));

    // Extract metadata
    // pdf-lib API has changed, so we need to extract metadata differently
    const title = pdfDoc.getTitle() || '';
    const author = pdfDoc.getAuthor() || '';
    const creator = pdfDoc.getCreator() || '';
    const producer = pdfDoc.getProducer() || '';
    const creationDate = pdfDoc.getCreationDate() ? new Date(pdfDoc.getCreationDate() as Date).toISOString() : null;
    const modificationDate = pdfDoc.getModificationDate() ? new Date(pdfDoc.getModificationDate() as Date).toISOString() : null;

    return {
      title: title || getFilenameFromPath(filePath),
      author: author || 'Unknown',
      creator: creator || 'Unknown',
      producer: producer || 'Unknown',
      creationDate: creationDate ? new Date(creationDate).toISOString() : null,
      modificationDate: modificationDate ? new Date(modificationDate).toISOString() : null,
      pageCount: pdfDoc.getPageCount(),
      fileSize: fileStats.size,
    };
  } catch (error) {
    console.error('Error extracting PDF metadata:', error);

    // Get file stats for basic info
    try {
      const fileStats = await RNFS.stat(filePath);

      // Return basic metadata if extraction fails
      return {
        title: getFilenameFromPath(filePath),
        author: 'Unknown',
        creator: 'Unknown',
        producer: 'Unknown',
        creationDate: null,
        modificationDate: null,
        pageCount: 1, // Assume at least one page
        fileSize: fileStats.size,
      };
    } catch (statsError) {
      console.error('Error getting file stats:', statsError);
      // Return minimal metadata if even stats fail
      return {
        title: getFilenameFromPath(filePath),
        author: 'Unknown',
        creator: 'Unknown',
        producer: 'Unknown',
        creationDate: null,
        modificationDate: null,
        pageCount: 1,
        fileSize: 0,
      };
    }
  }
};

// Helper function to get filename from path
export const getFilenameFromPath = (filePath: string): string => {
  const parts = filePath.split('/');
  const fullFilename = parts[parts.length - 1];

  // Remove timestamp prefix if it exists (from our naming convention)
  const filenameMatch = fullFilename.match(/^\d+_(.+)$/);
  return filenameMatch ? filenameMatch[1] : fullFilename;
};

// Get all PDF files in the app's document directory
export const getAllPDFFiles = async (): Promise<string[]> => {
  try {
    await ensureDirectoryExists();

    const files = await RNFS.readDir(APP_DOCUMENTS_DIR);
    return files
      .filter(file => file.name.toLowerCase().endsWith('.pdf'))
      .map(file => file.path);
  } catch (error) {
    console.error('Error getting PDF files:', error);
    return [];
  }
};
