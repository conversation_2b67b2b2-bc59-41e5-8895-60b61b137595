import * as RNFS from 'react-native-fs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { MODELS_DIR, ModelInfo, ModelConfig } from './onnxRuntime';
import RNFetchBlob from 'react-native-blob-util';

// Storage key for model metadata
const MODELS_STORAGE_KEY = 'pdf_viewer_models';

// Parse model configuration from config.json file
export const parseModelConfig = async (modelId: string): Promise<ModelConfig | null> => {
  try {
    // Get model metadata
    const models = await getModelMetadata();
    const model = models.find(m => m.id === modelId);

    if (!model) {
      console.error(`Model ${modelId} not found in metadata`);
      return null;
    }

    // If the model already has a modelConfig, use it directly
    if (model.modelConfig) {
      console.log(`Using existing model config for ${modelId}:`, model.modelConfig);
      return model.modelConfig;
    }

    // For LLaMA-style models, use architecture-specific values if config file doesn't exist
    if (modelId.toLowerCase().includes('llama')) {
      // These values are typical for small LLaMA variants (like TinyLlama)
      const llamaConfig: ModelConfig = {
        hiddenSize: 2048,
        numAttentionHeads: 32,
        numKeyValueHeads: 4,
        numHiddenLayers: 22,
        headDim: 64,
        intermediateSize: 5632,
        maxPositionEmbeddings: 2048
      };
      console.log(`Using architecture-specific config for ${modelId}:`, llamaConfig);
      return llamaConfig;
    }

    // Determine config file path
    const configPath = `${MODELS_DIR}/config.json`;

    // Check if config file exists
    const exists = await RNFS.exists(configPath);
    if (!exists) {
      console.warn(`Config file not found for model ${modelId}`);
      return null;
    }

    // Read and parse config file
    const configContent = await RNFS.readFile(configPath, 'utf8');
    const config = JSON.parse(configContent);

    // Extract key parameters
    const modelConfig: ModelConfig = {
      hiddenSize: config.hidden_size || 0,
      numAttentionHeads: config.num_attention_heads || 0,
      numKeyValueHeads: config.num_key_value_heads || config.num_attention_heads || 0,
      numHiddenLayers: config.num_hidden_layers || 0,
      intermediateSize: config.intermediate_size,
      maxPositionEmbeddings: config.max_position_embeddings
    };

    // Calculate derived values
    if (modelConfig.hiddenSize > 0 && modelConfig.numAttentionHeads > 0) {
      modelConfig.headDim = modelConfig.hiddenSize / modelConfig.numAttentionHeads;
    }

    console.log(`Parsed config for model ${modelId}:`, modelConfig);
    return modelConfig;
  } catch (error) {
    console.error(`Error parsing config for model ${modelId}:`, error);

    // Fallback for known model architectures even if there's an error
    if (modelId.toLowerCase().includes('llama')) {
      console.log(`Falling back to LLaMA architecture config after error`);
      return {
        hiddenSize: 2048,
        numAttentionHeads: 32,
        numKeyValueHeads: 4,
        numHiddenLayers: 22,
        headDim: 64,
        intermediateSize: 5632,
        maxPositionEmbeddings: 2048
      };
    } else if (modelId.toLowerCase().includes('phi')) {
      console.log(`Falling back to Phi architecture config after error`);
      return {
        hiddenSize: 2560,
        numAttentionHeads: 32,
        numKeyValueHeads: 32, // Phi uses MHA (Multi-Head Attention)
        numHiddenLayers: 32,
        headDim: 80,
        intermediateSize: 10240,
        maxPositionEmbeddings: 2048
      };
    }

    return null;
  }
};

// Load model with configuration
export const loadModelWithConfig = async (modelId: string): Promise<ModelInfo | null> => {
  try {
    // Ensure model is downloaded
    const downloaded = await ensureModelDownloaded(modelId);
    if (!downloaded) {
      return null;
    }

    // Get model metadata
    const models = await getModelMetadata();
    const model = models.find(m => m.id === modelId);

    if (!model) {
      return null;
    }

    // Parse model config
    const configId = `${modelId}-config`;
    await ensureModelDownloaded(configId);

    const modelConfig = await parseModelConfig(modelId);

    // Update model with config
    if (modelConfig) {
      await updateModelMetadata(modelId, { modelConfig });
      model.modelConfig = modelConfig;
    }

    return model;
  } catch (error) {
    console.error(`Error loading model with config: ${modelId}`, error);
    return null;
  }
};

// Get model dimensions from config or use defaults
export const getModelDimensions = async (modelId: string, session?: any): Promise<{
  numKeyValueHeads: number;
  numAttentionHeads: number;
  headDim: number;
}> => {
  try {
    // First check if session has model config (fastest path)
    if (session?.modelConfig) {
      const config = session.modelConfig;

      // Check if config uses camelCase or snake_case properties
      const hasSnakeCase = config.num_attention_heads !== undefined || config.num_key_value_heads !== undefined;
      const hasCamelCase = config.numAttentionHeads !== undefined || config.numKeyValueHeads !== undefined;

      // Extract values using the appropriate property naming convention
      let numAttentionHeads, numKeyValueHeads, hiddenSize, headDim;

      if (hasSnakeCase) {
        numAttentionHeads = config.num_attention_heads;
        numKeyValueHeads = config.num_key_value_heads || config.num_attention_heads;
        hiddenSize = config.hidden_size;
      } else if (hasCamelCase) {
        numAttentionHeads = config.numAttentionHeads;
        numKeyValueHeads = config.numKeyValueHeads || config.numAttentionHeads;
        hiddenSize = config.hiddenSize;
      } else {
        throw new Error(`Session config missing head counts for ${modelId}`);
      }

      // Validate required head counts
      if (!numAttentionHeads) {
        throw new Error(`Session config missing attention head count for ${modelId}`);
      }

      // Calculate head dimension from hidden size if not already provided
      if (config.headDim) {
        headDim = config.headDim;
      } else if (hiddenSize) {
        headDim = hiddenSize / numAttentionHeads;
      } else {
        // Default head dimension if we can't calculate it
        headDim = 64;
      }

      // Verify head counts are compatible for GQA (if using different counts)
      if (numKeyValueHeads !== numAttentionHeads && numAttentionHeads % numKeyValueHeads !== 0) {
        console.warn(
          `Warning: num_attention_heads (${numAttentionHeads}) should be divisible by ` +
          `num_key_value_heads (${numKeyValueHeads}). Using anyway, but this may cause issues.`
        );
      }

      console.log(`Using validated session config: numKeyValueHeads=${numKeyValueHeads}, numAttentionHeads=${numAttentionHeads}, headDim=${headDim}`);
      return {
        numKeyValueHeads,
        headDim,
        numAttentionHeads
      };
    }

    // If no session config, get model metadata
    const models = await getModelMetadata();
    const model = models.find(m => m.id === modelId);

    // If model config is available in metadata, use it
    if (model?.modelConfig) {
      const headDim = model.modelConfig.hiddenSize && model.modelConfig.numAttentionHeads
        ? model.modelConfig.hiddenSize / model.modelConfig.numAttentionHeads
        : 64; // Default to 64 if calculation not possible

      // For models like TinyLlama, there's a distinction between attention heads and key-value heads
      const numHeads = model.modelConfig.numKeyValueHeads || model.modelConfig.numAttentionHeads || 4;
      const numAttentionHeads = model.modelConfig.numAttentionHeads || numHeads;

      console.log(`Using model config from metadata: numKeyValueHeads=${numHeads}, numAttentionHeads=${numAttentionHeads}, headDim=${headDim}`);
      return {
        numKeyValueHeads: numHeads,
        numAttentionHeads,
        headDim
      };
    }

    // Fallback values for known model architectures
    // This approach is model-agnostic and based on architecture patterns rather than specific model names
    if (modelId.toLowerCase().includes('llama')) {
      // LLaMA-style models typically use GQA with 8:1 ratio (or 4:1 for smaller variants)
      console.log('Using values for LLaMA-style architecture: numKeyValueHeads=4, numAttentionHeads=32, headDim=64');
      return { numKeyValueHeads: 4, headDim: 64, numAttentionHeads: 32 };
    } else if (modelId.toLowerCase().includes('phi')) {
      // Phi models typically use MHA (Multi-Head Attention) where KV heads = attention heads
      console.log('Using values for Phi-style architecture: numKeyValueHeads=32, headDim=80, numAttentionHeads=32');
      return { numKeyValueHeads: 32, headDim: 80, numAttentionHeads: 32 };
    } else if (modelId.toLowerCase().includes('mistral')) {
      // Mistral models typically use GQA with an 8:1 ratio
      console.log('Using values for Mistral-style architecture: numKeyValueHeads=8, headDim=64, numAttentionHeads=32');
      return { numKeyValueHeads: 8, headDim: 64, numAttentionHeads: 32 };
    }

    // Default fallback values
    console.log('Using default fallback values: numKeyValueHeads=4, numAttentionHeads=4, headDim=64');
    return { numKeyValueHeads: 4, headDim: 64, numAttentionHeads: 4 };
  } catch (error) {
    console.error('Error getting model dimensions:', error);
    // Return safe default values
    return { numKeyValueHeads: 4, headDim: 64, numAttentionHeads: 4 };
  }
};

// Default models
export const DEFAULT_MODELS: ModelInfo[] = [
  // --- Embedding Model for PDF Vectorization ---
  // {
  //   id: 'all-MiniLM-L6-v2',
  //   name: 'MiniLM-L6 (Embedding)',
  //   description: 'Efficient embedding model for PDF text vectorization',
  //   path: `${MODELS_DIR}/all-MiniLM-L6-v2.onnx_data`,
  //   size: 90000000, // ~85MB
  //   version: '1.0.0',
  //   type: 'onnx',
  //   downloadUrl: 'https://huggingface.co/Xenova/all-MiniLM-L6-v2/resolve/main/onnx/model.onnx',
  //   isDownloaded: false,
  //   contextWindowSize: 512,
  //   modelType: 'embedding',
  //   // Let these be detected dynamically
  //   inputNames: ['input_ids', 'attention_mask', 'token_type_ids'],
  //   outputNames: ['last_hidden_state'],
  // },
  // {
  //   id: 'all-MiniLM-L6-v2-tokenizer',
  //   name: 'MiniLM-L6 Tokenizer',
  //   description: 'Tokenizer for MiniLM-L6 embedding model',
  //   path: `${MODELS_DIR}/all-MiniLM-L6-v2-tokenizer.json`,
  //   size: 800000,
  //   version: '1.0.0',
  //   type: 'tokenizer',
  //   downloadUrl: 'https://huggingface.co/Xenova/all-MiniLM-L6-v2/resolve/main/tokenizer.json',
  //   isDownloaded: false,
  // },

  // // --- Generative Models for RAG Response Generation ---
  // {
  //   id: 'phi-2',
  //   name: 'Phi-2 (Generative)',
  //   description: 'Microsoft Phi-2 2.7B model optimized for mobile RAG',
  //   path: `${MODELS_DIR}/phi-2.onnx`,
  //   size: 1500000000, // ~1.5GB (quantized)
  //   version: '1.0.0',
  //   type: 'onnx',
  //   downloadUrl: 'https://huggingface.co/microsoft/phi-2-onnx/resolve/main/model_quantized.onnx',
  //   // Alternative URLs if the primary one fails
  //   alternativeUrls: [
  //     'https://huggingface.co/TheBloke/phi-2-ONNX/resolve/main/model_quantized.onnx',
  //     'https://huggingface.co/onnx-community/phi-2/resolve/main/model_quantized.onnx'
  //   ],
  //   isDownloaded: false,
  //   contextWindowSize: 2048,
  //   modelType: 'generative',
  //   // Let these be detected dynamically
  //   inputNames: ['input_ids', 'attention_mask'],
  //   outputNames: ['logits'],
  // },
  // {
  //   id: 'phi-2-tokenizer',
  //   name: 'Phi-2 Tokenizer',
  //   description: 'Tokenizer for Phi-2 generative model',
  //   path: `${MODELS_DIR}/phi-2-tokenizer.json`,
  //   size: 900000,
  //   version: '1.0.0',
  //   type: 'tokenizer',
  //   downloadUrl: 'https://huggingface.co/shoibl/phi2_onnx/resolve/main/tokenizer.json',
  //   isDownloaded: false,
  // },

  {
    id: 'tinyllama-1.1b-chat',
    name: 'TinyLlama 1.1B Chat (Generative)',
    description: 'Lightweight generative model for mobile RAG chat',
    path: `${MODELS_DIR}/tinyllama-1.1b-chat.onnx`,
    size: 1100000000, // ~1.1GB (quantized)
    version: '1.0.0',
    type: 'onnx',
    // Verified download URL with checksum
    downloadUrl: 'https://huggingface.co/onnx-community/TinyLlama-1.1B-Chat-v1.0-ONNX/resolve/main/onnx/model.onnx',
    isDownloaded: false,
    contextWindowSize: 2048,
    modelType: 'generative',
    // Explicitly define model configuration for TinyLlama
    modelConfig: {
      hiddenSize: 2048,
      numAttentionHeads: 32,
      numKeyValueHeads: 4,
      numHiddenLayers: 22,
      headDim: 64,
      intermediateSize: 5632,
      maxPositionEmbeddings: 2048
    },
    // Let these be detected dynamically
    inputNames: ['input_ids', 'attention_mask', 'position_ids'],
    outputNames: ['logits'],
  },
  {
    id: 'tinyllama-1.1b-chat-config',
    name: 'TinyLlama 1.1B Chat (config)',
    description: 'Configuration file for TinyLlama 1.1B Chat model',
    path: `${MODELS_DIR}/config.json`,
    size: 5000, // ~5KB
    version: '1.0.0',
    type: 'config',
    // Verified download URL with checksum
    downloadUrl: 'https://huggingface.co/onnx-community/TinyLlama-1.1B-Chat-v1.0-ONNX/resolve/main/config.json',
    isDownloaded: false,
    // Include the actual config content as a fallback
    modelConfig: {
      hiddenSize: 2048,
      numAttentionHeads: 32,
      numKeyValueHeads: 4,
      numHiddenLayers: 22,
      headDim: 64,
      intermediateSize: 5632,
      maxPositionEmbeddings: 2048
    }
  },
  {
    id: 'tinyllama-1.1b-chat-data',
    name: 'TinyLlama 1.1B Chat (Generative)',
    description: 'Lightweight generative model for mobile RAG chat',
    path: `${MODELS_DIR}/model.onnx_data`,
    size: 1100000000, // ~1.1GB (quantized)
    version: '1.0.0',
    type: 'onnx_data',
    // Verified download URL with checksum
    downloadUrl: 'https://huggingface.co/onnx-community/TinyLlama-1.1B-Chat-v1.0-ONNX/resolve/main/onnx/model.onnx_data',
    // Mirror with same checksum
    alternativeUrls: [
      'https://huggingface.co/onnx-community/TinyLlama-1.1B-Chat-v1.0-ONNX/resolve/main/onnx/model.onnx'
    ],
    isDownloaded: false,
    contextWindowSize: 2048,
    modelType: 'generative',
    // Let these be detected dynamically
    inputNames: ['input_ids', 'attention_mask'],
    outputNames: ['logits'],
  },
  {
    id: 'tinyllama-1.1b-chat-tokenizer',
    name: 'TinyLlama Tokenizer',
    description: 'Tokenizer for TinyLlama 1.1B Chat model',
    path: `${MODELS_DIR}/tokenizer.json`,
    size: 1000000,
    version: '1.0.0',
    type: 'tokenizer',
    downloadUrl: 'https://huggingface.co/onnx-community/TinyLlama-1.1B-Chat-v1.0-ONNX/resolve/main/tokenizer.json',
    isDownloaded: false,
  },

  // --- Fallback Model ---
  {
    id: 'mock-rag-model',
    name: 'Mock RAG Model (Fallback)',
    description: 'A fallback model for RAG when real models are not available',
    path: `${MODELS_DIR}/mock-rag-model.json`, // Not a real ONNX file
    size: 1000, // Approx bytes
    version: '1.0.0',
    type: 'onnx', // Still marked as ONNX type for logic flow
    isDownloaded: true, // Always considered downloaded
    isFallback: true,
    contextWindowSize: 768,
    modelType: 'generative', // Acts like a generative model
    inputNames: [], // Not applicable
    outputNames: [], // Not applicable
  },
];

// Save model metadata to AsyncStorage
export const saveModelMetadata = async (models: ModelInfo[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(MODELS_STORAGE_KEY, JSON.stringify(models));
  } catch (error) {
    console.error('Error saving model metadata:', error);
    throw error;
  }
};

// Get model metadata from AsyncStorage
export const getModelMetadata = async (): Promise<ModelInfo[]> => {
  try {
    const data = await AsyncStorage.getItem(MODELS_STORAGE_KEY);

    if (!data) {
      // Initialize with default models if no data exists
      await saveModelMetadata(DEFAULT_MODELS);
      return DEFAULT_MODELS;
    }

    const models = JSON.parse(data);

    // Check if models exist and update their status
    for (const model of models) {
      const exists = await RNFS.exists(model.path);
      if (exists !== model.isDownloaded) {
        model.isDownloaded = exists;
      }

      // Ensure alternativeUrls is properly set from DEFAULT_MODELS
      // This ensures we don't lose the alternativeUrls when loading from storage
      const defaultModel = DEFAULT_MODELS.find(m => m.id === model.id);
      if (defaultModel && defaultModel.alternativeUrls && !model.alternativeUrls) {
        console.log(`Adding alternativeUrls to model ${model.id} from DEFAULT_MODELS`);
        model.alternativeUrls = defaultModel.alternativeUrls;
      }
    }

    // Save updated status
    await saveModelMetadata(models);

    return models;
  } catch (error) {
    console.error('Error getting model metadata:', error);
    return DEFAULT_MODELS;
  }
};

// Update model metadata
export const updateModelMetadata = async (modelId: string, updates: Partial<ModelInfo>): Promise<ModelInfo | null> => {
  try {
    const models = await getModelMetadata();
    const index = models.findIndex(model => model.id === modelId);

    if (index === -1) {
      return null;
    }

    // Update the model
    models[index] = { ...models[index], ...updates };

    // Save updated models list
    await saveModelMetadata(models);

    return models[index];
  } catch (error) {
    console.error('Error updating model metadata:', error);
    throw error;
  }
};

// Check if a model is downloaded
export const isModelDownloaded = async (modelId: string): Promise<boolean> => {
  try {
    const models = await getModelMetadata();
    const model = models.find(m => m.id === modelId);

    if (!model) {
      return false;
    }

    // Check if the file exists
    const exists = await RNFS.exists(model.path);

    // Update model metadata if needed
    if (exists !== model.isDownloaded) {
      await updateModelMetadata(modelId, { isDownloaded: exists });
    }

    return exists;
  } catch (error) {
    console.error('Error checking if model is downloaded:', error);
    return false;
  }
};
// // Calculate SHA256 hash of a file using RNFetchBlob
// export const calculateSHA256 = async (filePath: string): Promise<string> => {
//   try {
//     console.log(`Calculating SHA256 hash for ${filePath}...`);

//     // Use RNFetchBlob to calculate hash (much faster than reading and calculating in JS)
//     const hash = await RNFetchBlob.fs.hash(filePath, 'sha256');

//     console.log(`SHA256 hash for ${filePath}: ${hash}`);
//     return hash;
//   } catch (error) {
//     console.error(`Error calculating SHA256 hash for ${filePath}:`, error);
//     return '';
//   }
// };

// Verify ONNX model
export const verifyOnnxModel = async (
  modelPath: string,
  expectedSize?: number,
  expectedHash?: string,
  isPartialDownload: boolean = false
): Promise<boolean> => {
  try {
    console.log('Verifying ONNX model:', {
      path: modelPath,
      platform: Platform.OS,
      expectedSize: expectedSize || 'unknown',
    });

    // Check if file exists
    const exists = await RNFS.exists(modelPath);
    if (!exists) {
      console.error(`Model file not found at ${modelPath}`);

      // On Android, also check for the alternative extension
      if (Platform.OS === 'android') {
        const alternativePath = modelPath.replace('.onnx_data', '.onnx');
        const alternativeExists = await RNFS.exists(alternativePath);
        if (alternativeExists) {
          console.log(`Found model with alternative extension at ${alternativePath}`);
          // Move file to correct extension
          await RNFS.moveFile(alternativePath, modelPath);
          console.log(`Moved model file to correct extension: ${modelPath}`);
          return true;
        }
      }
      return false;
    }

    // Check file size
    const fileInfo = await RNFS.stat(modelPath);
    const fileSize = parseInt(String(fileInfo.size || '0'));
    console.log('Model file info:', {
      size: fileSize,
      path: modelPath,
      lastModified: fileInfo.mtime,
    });

    // Basic size check - should be at least 1MB for any model
    if (fileSize < 1024 * 1024) {
      console.error(`Model file too small: ${fileSize} bytes`);
      return false;
    }

    // If we have an expected size, check if the file is at least 90% complete
    if (expectedSize && fileSize < expectedSize * 0.9) {
      console.error(`Model file incomplete: ${fileSize} bytes (expected at least ${expectedSize * 0.9} bytes)`);
      return false;
    }

    // For ONNX files, try to read the first few bytes to check if it's a valid file format
    try {
      // Read the first 16 bytes of the file to check for ONNX magic number or zip file signature
      const header = await RNFS.read(modelPath, 16, 0, 'ascii');

      // ONNX files typically start with specific binary patterns
      // PK is the signature for ZIP files (some ONNX models are packaged as ZIP)
      // 0x08 0x00 is common in protobuf files
      const validSignatures = ['ONNX', 'PK', String.fromCharCode(0x08, 0x00)];
      let isValidHeader = false;

      for (const signature of validSignatures) {
        if (header.includes(signature)) {
          console.log(`File header contains valid signature: ${signature}`);
          isValidHeader = true;
          break;
        }
      }

      if (!isValidHeader) {
        console.error('File header does not match any known ONNX format');

        // For interrupted downloads, we need to be strict about headers
        if (fileSize > 500 * 1024 * 1024 && !isPartialDownload) {
          console.warn('Large file with unknown header format - will attempt to use it anyway');
        } else {
          console.error('Invalid file header detected - rejecting file');
          return false;
        }
      }
    } catch (headerError) {
      console.warn('Could not read file header:', headerError);
      // Continue with verification despite header check failure
    }

    // For a more thorough check, try to read a small chunk from the middle of the file
    // This helps detect truncated or corrupted files
    try {
      const middleOffset = Math.floor(fileSize / 2);
      await RNFS.read(modelPath, 16, middleOffset, 'ascii');
      console.log(`Successfully read chunk from middle of file at offset ${middleOffset}`);
    } catch (readError) {
      console.error('Failed to read from middle of file - file may be corrupted:', readError);
      return false;
    }

    // // If an expected hash is provided, verify the file checksum
    // if (expectedHash) {
    //   try {
    //     const fileHash = await calculateSHA256(modelPath);
    //     if (fileHash && fileHash !== expectedHash) {
    //       console.error(`Checksum mismatch: expected ${expectedHash}, got ${fileHash}`);
    //       return false;
    //     } else if (fileHash) {
    //       console.log(`Checksum verified: ${fileHash}`);
    //     }
    //   } catch (hashError) {
    //     console.warn('Could not verify file hash:', hashError);
    //     // Continue even if hash verification fails
    //   }
    // }
    // Return success after basic verification
    console.log(`Model verification successful: ${modelPath}`);
    return true;
  } catch (error) {
    console.error(`Error verifying model: ${error}`);
    return false;
  }
};

// Maximum number of download retries
export const MAX_DOWNLOAD_RETRIES = 3;

// Download a model
export const downloadModel = async (
  modelId: string,
  progressCallback?: (progress: number) => void
): Promise<boolean> => {
  let model: ModelInfo | undefined;

  try {
    const models = await getModelMetadata();
    model = models.find(m => m.id === modelId);

    if (!model || !model.downloadUrl) {
      throw new Error(`Model ${modelId} not found or has no download URL`);
    }

    // Ensure models directory exists with proper permissions
    try {
      await RNFS.mkdir(MODELS_DIR, { NSFileProtectionKey: 'NSFileProtectionComplete' });

      if (Platform.OS === 'android') {
        // On Android, ensure the directory is accessible for file operations
        await RNFS.exists(MODELS_DIR); // This forces proper directory permissions
      }
    } catch (dirError) {
      console.warn(`Error creating models directory: ${dirError}`);
      // Continue anyway as the directory might already exist
    }


    // Create download directory if it doesn't exist
    await RNFS.mkdir(MODELS_DIR);

    // Delete existing file if it exists
    const exists = await RNFS.exists(model.path);
    if (exists) {
      await RNFS.unlink(model.path);
      console.log(`Deleted existing model file: ${model.path}`);
    }

    // Log download attempt
    console.log(`Downloading model from ${model.downloadUrl} to ${model.path}`);

    // Download the model with improved configuration
    const response = await RNFetchBlob.config({
      fileCache: true,
      path: model.path,
      overwrite: true,
      addAndroidDownloads: {
        useDownloadManager: true,
        notification: true,
        title: model.name,
        description: model.description,
        mediaScannable: false,
        path:RNFetchBlob.fs.dirs.DownloadDir+"/data"
      },
    })
    .fetch('GET', model.downloadUrl)
    .progress({ count: 10 }, (received: number, total: number) => {
      // Only update progress if total is valid
      if (total > 0) {
        const progress = received / total;
        console.log(`Download progress: ${Math.round(progress * 100)}% (${received}/${total} bytes)`);
        if (progressCallback) {
          progressCallback(progress);
        }
      } else {
        // If total is unknown, provide indeterminate progress updates
        console.log(`Downloaded ${received} bytes (total size unknown)`);
        if (progressCallback && received % 1000000 === 0) { // Update every 1MB
          progressCallback(0.5); // Indeterminate progress
        }
      }
    }).catch(async (error) => {
      console.error(`Download failed: ${error}`);
      throw error;
    });

    console.log('Download complete');
    const downloadedPath =  response.path();
    console.log(`Downloaded model to: ${downloadedPath},`, await RNFetchBlob.fs.exists(response.path()));

    // Verify the downloaded file exists
    const downloadExists = await RNFS.exists(downloadedPath);

    if (!downloadExists) {
      throw new Error(`Downloaded file not found at ${downloadedPath}`);
    }

    // Move the file to its final location if needed
    if (downloadedPath !== model.path) {
      // Extract the model ID from the path
      const modelId = model.id;
      const expectedPath = model.path;

      // If the downloaded file has a generic name like 'model.onnx', rename it to include the model ID
      if (downloadedPath.includes('model.onnx') && !downloadedPath.includes(modelId)) {
        console.log(`Renaming generic model file to include model ID: ${modelId}`);
      }

      console.log(`Moving file from ${downloadedPath} to ${expectedPath}`);
      try {
        await RNFS.moveFile(downloadedPath, model.path);
      } catch (moveError) {
        console.error(`Error moving file: ${moveError}`);

        // As a fallback, try to copy the file instead
        console.log('Attempting to copy file instead...');
        await RNFS.copyFile(downloadedPath, model.path);
        await RNFS.unlink(downloadedPath);
      }
    }
    // Check file size after download
    const fileInfo = await RNFS.stat(model.path);
    const fileSize = parseInt(String(fileInfo.size || '0'));
    console.log(`Downloaded file size: ${fileSize} bytes`);



    // Verify the file exists at the final location
    const finalExists = await RNFS.exists(model.path);
    if (!finalExists) {
      throw new Error(`Model file not found at final location: ${model.path}`);
    }

    // Update model metadata
    await updateModelMetadata(modelId, {
      isDownloaded: true,
      lastUpdated: new Date().toISOString(),
    });

    console.log(`Model ${modelId} successfully downloaded and verified`);
    return true;
  } catch (error) {
    console.error(`Error downloading model ${modelId}:`, error);
    // Clean up any partially downloaded file
    try {
      if (model?.path && await RNFS.exists(model.path)) {
        await RNFS.unlink(model.path);
      }
    } catch (cleanupError) {
      console.warn('Error cleaning up failed download:', cleanupError);
    }
    throw error;
  }
};

// Verify and clean up invalid model files
export const verifyAndCleanupModel = async (modelId: string): Promise<boolean> => {
  try {
    console.log(`Verifying model ${modelId} integrity...`);
    const models = await getModelMetadata();
    const model = models.find(m => m.id === modelId);

    if (!model) {
      console.error(`Model ${modelId} not found in metadata`);
      return false;
    }

    // Check if the file exists
    const exists = await RNFS.exists(model.path);
    if (!exists) {
      console.log(`Model file ${model.path} does not exist, no cleanup needed`);
      // Update metadata to reflect that the model is not downloaded
      await updateModelMetadata(modelId, { isDownloaded: false });
      return false;
    }
return true;
  } catch (error) {
    console.error(`Error in verifyAndCleanupModel for ${modelId}:`, error);
    return false;
  }
};

// Ensure model is downloaded
export const ensureModelDownloaded = async (
  modelId: string,
  progressCallback?: (progress: number, attempt?: number) => void
): Promise<boolean> => {
  try {
    // First, verify and cleanup any invalid model files
    await verifyAndCleanupModel(modelId);

    // Check if model is already downloaded
    const isDownloaded = await isModelDownloaded(modelId);

    if (isDownloaded) {
      return true;
    }

    // Download the model if not already downloaded
    return await downloadModel(modelId, progressCallback);
  } catch (error) {
    console.error(`Error ensuring model ${modelId} is downloaded:`, error);
    return false;
  }
};

// Delete a model
export const deleteModel = async (modelId: string): Promise<boolean> => {
  try {
    const models = await getModelMetadata();
    const model = models.find(m => m.id === modelId);

    if (!model) {
      throw new Error(`Model ${modelId} not found`);
    }

    // Check if the file exists
    const exists = await RNFS.exists(model.path);

    if (exists) {
      // Delete the file
      await RNFS.unlink(model.path);
    }

    // Update model metadata
    await updateModelMetadata(modelId, {
      isDownloaded: false,
      lastUpdated: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error(`Error deleting model ${modelId}:`, error);
    throw error;
  }
};

// Get model download status
export const getModelDownloadStatus = async (): Promise<{
  [modelId: string]: {
    isDownloaded: boolean;
    path: string;
  };
}> => {
  try {
    const models = await getModelMetadata();
    const status: {
      [modelId: string]: {
        isDownloaded: boolean;
        path: string;
      };
    } = {};

    for (const model of models) {
      const exists = await RNFS.exists(model.path);

      status[model.id] = {
        isDownloaded: exists,
        path: model.path,
      };

      // Update model metadata if needed
      if (exists !== model.isDownloaded) {
        await updateModelMetadata(model.id, { isDownloaded: exists });
      }
    }

    return status;
  } catch (error) {
    console.error('Error getting model download status:', error);
    return {};
  }
};
