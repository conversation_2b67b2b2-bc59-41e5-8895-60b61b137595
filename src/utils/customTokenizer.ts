import RNFS from 'react-native-fs';
import { getModelMetadata } from './modelManager';
import { APP_DOCUMENTS_DIR } from './fileUtils';

// Define the models directory
const MODELS_DIR = `${APP_DOCUMENTS_DIR}/models`;

// Track the active model ID for tokenizer selection
let activeModelId: string | null = null;
// Simple tokenizer interface
export interface SimpleTokenizer {
  encode: (text: string) => Promise<number[]>;
  decode: (tokens: number[]) => Promise<string>;
}

// Basic character-level tokenizer
export class BasicTokenizer implements SimpleTokenizer {
  private vocabulary: Map<string, number>;
  private idToToken: Map<number, string>;

  constructor() {
    this.vocabulary = new Map();
    this.idToToken = new Map();

    // Initialize with basic ASCII characters
    const basicTokens = [
      '<s>', '</s>', '<pad>', '<unk>', ' ', '!', '"', '#', '$', '%', '&', '\'',
      '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7',
      '8', '9', ':', ';', '<', '=', '>', '?', '@', '[', '\\', ']', '^', '_', '`',
      '{', '|', '}', '~'
    ];

    // Add lowercase letters
    for (let i = 97; i <= 122; i++) {
      basicTokens.push(String.fromCharCode(i));
    }

    // Add uppercase letters
    for (let i = 65; i <= 90; i++) {
      basicTokens.push(String.fromCharCode(i));
    }

    // Add common words and subwords
    const commonWords = [
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'I', 'it', 'for', 'not', 'on', 'with',
      'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she',
      'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up', 'out', 'if', 'about',
      'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take',
      'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other', 'than', 'then', 'now', 'look',
      'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well',
      'way', 'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us'
    ];

    // Add common tokens for LLM models
    const llmTokens = [
      '<|im_start|>', '<|im_end|>', '<|assistant|>', '<|user|>', '<|system|>',
      'system', 'user', 'assistant', 'function', 'tool'
    ];

    // Combine all tokens
    const allTokens = [...basicTokens, ...commonWords, ...llmTokens];

    // Create vocabulary
    allTokens.forEach((token, index) => {
      this.vocabulary.set(token, index);
      this.idToToken.set(index, token);
    });
  }

  async loadFromFile(path: string): Promise<void> {
    try {
      // Check if file exists
      const exists = await RNFS.exists(path);
      if (!exists) {
        throw new Error(`Tokenizer file not found at path: ${path}`);
      }

      // Load vocabulary from file
      const content = await RNFS.readFile(path, 'utf8');
      const vocab = JSON.parse(content);

      // Clear existing vocabulary
      this.vocabulary.clear();
      this.idToToken.clear();

      // Load new vocabulary
      Object.entries(vocab).forEach(([token, id]) => {
        const tokenId = typeof id === 'number' ? id : parseInt(id as string);
        this.vocabulary.set(token, tokenId);
        this.idToToken.set(tokenId, token);
      });

      console.log(`Loaded vocabulary with ${this.vocabulary.size} tokens`);
    } catch (error) {
      console.error('Error loading tokenizer from file:', error);
      throw error;
    }
  }

  async encode(text: string): Promise<number[]> {
    // Simple word-level tokenization
    const tokens: number[] = [];
    const words = text.split(/(\s+|[.,!?;:'"(){}\[\]<>])/);

    for (const word of words) {
      if (!word) continue;

      if (this.vocabulary.has(word)) {
        tokens.push(this.vocabulary.get(word)!);
      } else {
        // Character-level fallback
        for (const char of word) {
          const id = this.vocabulary.get(char) || this.vocabulary.get('<unk>');
          if (id !== undefined) {
            tokens.push(id);
          } else {
            tokens.push(this.vocabulary.get('<unk>')!);
          }
        }
      }
    }

    return tokens;
  }

  async decode(tokens: number[]): Promise<string> {
    return tokens.map(id => this.idToToken.get(id) || '').join('');
  }
}

// Map to store tokenizers by model ID
const tokenizerCache: Map<string, SimpleTokenizer> = new Map();
let defaultTokenizer: SimpleTokenizer | null = null;

// Get the appropriate tokenizer path for a model ID
export const getTokenizerPathForModel = async (modelId: string): Promise<string | null> => {
  try {
    const models = await getModelMetadata();
    
    // First, look for a tokenizer specifically for this model
    const tokenizer = models.find(m =>
      m.type === 'tokenizer' &&
      m.id.includes(modelId.replace('-onnx', '').replace('-chat', '')) &&
      m.isDownloaded
    );
    
    if (tokenizer) {
      console.log(`Found specific tokenizer for model ${modelId}: ${tokenizer.id}`);
      return tokenizer.path;
    }
    
    // If no specific tokenizer found, look for any downloaded tokenizer
    const anyTokenizer = models.find(m => m.type === 'tokenizer' && m.isDownloaded);
    if (anyTokenizer) {
      console.log(`Using generic tokenizer ${anyTokenizer.id} for model ${modelId}`);
      return anyTokenizer.path;
    }
    
    console.log(`No tokenizer found for model ${modelId}`);
    return null;
  } catch (error) {
    console.error('Error getting tokenizer path for model:', error);
    return null;
  }
};

// Initialize tokenizer
export const initTokenizer = async (modelId?: string, tokenizerPath?: string): Promise<SimpleTokenizer> => {
  try {
    // If modelId is provided, set it as active
    if (modelId) {
      activeModelId = modelId;
    }
    
    // Check if we already have a cached tokenizer for this model
    if (modelId && tokenizerCache.has(modelId)) {
      console.log(`Using cached tokenizer for model ${modelId}`);
      return tokenizerCache.get(modelId)!;
    }
    
    // Create a basic tokenizer
    const basicTokenizer = new BasicTokenizer();
    
    // If no explicit tokenizer path is provided but we have a model ID,
    // try to find the appropriate tokenizer for this model
    if (!tokenizerPath && modelId) {
      tokenizerPath = await getTokenizerPathForModel(modelId);
    }

    if (tokenizerPath) {
      // Try to load from file
      try {
        await basicTokenizer.loadFromFile(tokenizerPath);
        console.log(`Loaded tokenizer from ${tokenizerPath}`);
        
        // Cache the tokenizer if we have a model ID
        if (modelId) {
          tokenizerCache.set(modelId, basicTokenizer);
        }
      } catch (error) {
        console.warn(`Failed to load tokenizer from ${tokenizerPath}, using basic tokenizer instead:`, error);
      }
    }

    // Store as default if no model-specific tokenizer is set
    if (!modelId) {
      defaultTokenizer = basicTokenizer;
    }
    
    return basicTokenizer;
  } catch (error) {
    console.error('Error initializing tokenizer:', error);
    throw error;
  }
};

// Get tokenizer for a specific model or the default tokenizer
export const getTokenizer = async (modelId?: string): Promise<SimpleTokenizer> => {
  // If model ID is provided, try to get or initialize that specific tokenizer
  if (modelId) {
    // Check if we have a cached tokenizer for this model
    if (tokenizerCache.has(modelId)) {
      return tokenizerCache.get(modelId)!;
    }
    
    // Try to initialize a tokenizer for this model
    return initTokenizer(modelId);
  }
  
  // If no model ID is provided but we have an active model ID, use that
  if (activeModelId && tokenizerCache.has(activeModelId)) {
    return tokenizerCache.get(activeModelId)!;
  }
  
  // If we have a default tokenizer, use that
  if (defaultTokenizer) {
    return defaultTokenizer;
  }
  
  // Initialize a basic tokenizer as a last resort
  return initTokenizer();
};

// Encode text to token IDs
export const encodeText = async (text: string, modelId?: string): Promise<number[]> => {
  try {
    // Use the provided model ID or the active model ID
    const effectiveModelId = modelId || activeModelId;
    const tokenizerInstance = await getTokenizer(effectiveModelId);
    return await tokenizerInstance.encode(text);
  } catch (error) {
    console.error('Error encoding text:', error);
    throw error;
  }
};

// Decode token IDs to text
export const decodeTokens = async (tokenIds: number[], modelId?: string): Promise<string> => {
  try {
    // Use the provided model ID or the active model ID
    const effectiveModelId = modelId || activeModelId;
    const tokenizerInstance = await getTokenizer(effectiveModelId);
    return await tokenizerInstance.decode(tokenIds);
  } catch (error) {
    console.error('Error decoding tokens:', error);
    throw error;
  }
};

// Count tokens in text
export const countTokens = async (text: string, modelId?: string): Promise<number> => {
  try {
    const tokenIds = await encodeText(text, modelId);
    return tokenIds.length;
  } catch (error) {
    console.error('Error counting tokens:', error);
    throw error;
  }
};

// Set the active model ID for tokenization
export const setActiveModelId = (modelId: string): void => {
  activeModelId = modelId;
  console.log(`Set active model ID for tokenization to: ${modelId}`);
};

// Get the active model ID
export const getActiveModelId = (): string | null => {
  return activeModelId;
};
