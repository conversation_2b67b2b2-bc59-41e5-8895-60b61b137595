import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { APP_DOCUMENTS_DIR, ensureDirectoryExists } from './fileUtils';
import { addDocument } from './documentStorage';

// Sample PDF files in the assets directory
const SAMPLE_PDFS = [
  {
    name: 'sample.pdf',
    assetPath: 'src/assets/sample.pdf',
  },
];

/**
 * Copy sample PDF files from assets to the app's documents directory
 * This is useful for first-time app launch to provide some sample content
 */
export const copySamplePDFs = async (): Promise<void> => {
  try {
    // Ensure the documents directory exists
    await ensureDirectoryExists();

    // Check if we already have documents
    const files = await RNFS.readDir(APP_DOCUMENTS_DIR);
    const hasPDFs = files.some(file => file.name.toLowerCase().endsWith('.pdf'));

    // If we already have PDFs, don't copy samples
    if (hasPDFs) {
      console.log('PDF files already exist, skipping sample copy');
      return;
    }

    // Copy each sample PDF
    for (const sample of SAMPLE_PDFS) {
      try {
        // Get the full path to the asset
        // For Android, we need to use the asset:/ prefix
        // For iOS, we need to use the app bundle path
        const assetPath = Platform.OS === 'android'
          ? `asset:/${sample.assetPath}`
          : `${RNFS.MainBundlePath}/${sample.assetPath}`;

        // Create a destination path
        const destPath = `${APP_DOCUMENTS_DIR}/${Date.now()}_${sample.name}`;

        try {
          // Check if the source file exists
          const exists = await RNFS.exists(assetPath);
          if (!exists) {
            console.error(`Sample PDF not found at path: ${assetPath}`);
            continue;
          }

          // Copy the file
          await RNFS.copyFile(assetPath, destPath);
          console.log(`Copied sample PDF: ${sample.name} to ${destPath}`);

          // Add to document storage
          await addDocument(destPath);
        } catch (copyError) {
          console.error(`Error copying file from ${assetPath} to ${destPath}:`, copyError);
        }
      } catch (error) {
        console.error(`Error copying sample PDF ${sample.name}:`, error);
      }
    }
  } catch (error) {
    console.error('Error copying sample PDFs:', error);
  }
};
