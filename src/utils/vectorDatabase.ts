import SQLite from 'react-native-sqlite-storage';
import RNFS from 'react-native-fs';
import { APP_DOCUMENTS_DIR } from './fileUtils';
import { TextChunk } from './textExtraction';

// Enable SQLite debugging in development
SQLite.DEBUG(true);
SQLite.enablePromise(true);

// Database file path
const DB_NAME = 'pdf_vector_db.sqlite';
const DB_PATH = `${APP_DOCUMENTS_DIR}/${DB_NAME}`;

// Database connection
let database: SQLite.SQLiteDatabase | null = null;

/**
 * Initialize the database
 */
export const initDatabase = async (): Promise<SQLite.SQLiteDatabase> => {
  try {
    // Ensure the app documents directory exists
    await RNFS.mkdir(APP_DOCUMENTS_DIR);
    
    // Open or create the database
    database = await SQLite.openDatabase({
      name: DB_NAME,
      location: 'Documents',
    });
    
    // Create tables if they don't exist
    await createTables(database);
    
    return database;
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

/**
 * Create database tables
 */
const createTables = async (db: SQLite.SQLiteDatabase): Promise<void> => {
  try {
    // Create documents table
    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS documents (
        id TEXT PRIMARY KEY,
        path TEXT NOT NULL,
        title TEXT NOT NULL,
        author TEXT,
        page_count INTEGER,
        file_size INTEGER,
        last_opened TEXT,
        added_date TEXT NOT NULL,
        is_processed BOOLEAN DEFAULT 0,
        processing_error TEXT,
        last_updated TEXT
      );
    `);
    
    // Create text_chunks table
    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS text_chunks (
        id TEXT PRIMARY KEY,
        document_id TEXT NOT NULL,
        text TEXT NOT NULL,
        page_number INTEGER,
        position_x REAL,
        position_y REAL,
        position_width REAL,
        position_height REAL,
        metadata TEXT,
        FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
      );
    `);
    
    // Create vector_embeddings table
    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS vector_embeddings (
        id TEXT PRIMARY KEY,
        chunk_id TEXT NOT NULL,
        document_id TEXT NOT NULL,
        embedding BLOB NOT NULL,
        embedding_model TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (chunk_id) REFERENCES text_chunks (id) ON DELETE CASCADE,
        FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
      );
    `);
    
    // Create search_cache table
    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS search_cache (
        id TEXT PRIMARY KEY,
        query TEXT NOT NULL,
        results TEXT NOT NULL,
        created_at TEXT NOT NULL,
        expires_at TEXT NOT NULL
      );
    `);
    
    console.log('Database tables created successfully');
  } catch (error) {
    console.error('Error creating tables:', error);
    throw error;
  }
};

/**
 * Close the database connection
 */
export const closeDatabase = async (): Promise<void> => {
  try {
    if (database) {
      await database.close();
      database = null;
    }
  } catch (error) {
    console.error('Error closing database:', error);
    throw error;
  }
};

/**
 * Store document text chunks in the database
 */
export const storeDocumentTextChunks = async (
  documentId: string,
  chunks: TextChunk[]
): Promise<void> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    // Begin transaction
    await database.transaction(async (tx) => {
      // Delete existing chunks for this document
      await tx.executeSql(
        'DELETE FROM text_chunks WHERE document_id = ?',
        [documentId]
      );
      
      // Insert new chunks
      for (const chunk of chunks) {
        await tx.executeSql(
          `INSERT INTO text_chunks (
            id, document_id, text, page_number, 
            position_x, position_y, position_width, position_height,
            metadata
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            chunk.id,
            documentId,
            chunk.text,
            chunk.pageNumber,
            chunk.position.x,
            chunk.position.y,
            chunk.position.width,
            chunk.position.height,
            chunk.metadata ? JSON.stringify(chunk.metadata) : null
          ]
        );
      }
      
      // Update document processing status
      await tx.executeSql(
        `UPDATE documents SET 
          is_processed = 1,
          last_updated = ?
         WHERE id = ?`,
        [new Date().toISOString(), documentId]
      );
    });
    
    console.log(`Stored ${chunks.length} text chunks for document ${documentId}`);
  } catch (error) {
    console.error('Error storing document text chunks:', error);
    throw error;
  }
};

/**
 * Get text chunks for a document
 */
export const getDocumentTextChunks = async (documentId: string): Promise<TextChunk[]> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    const [results] = await database.executeSql(
      `SELECT * FROM text_chunks WHERE document_id = ? ORDER BY page_number, position_y`,
      [documentId]
    );
    
    const chunks: TextChunk[] = [];
    for (let i = 0; i < results.rows.length; i++) {
      const row = results.rows.item(i);
      chunks.push({
        id: row.id,
        text: row.text,
        pageNumber: row.page_number,
        position: {
          x: row.position_x,
          y: row.position_y,
          width: row.position_width,
          height: row.position_height
        },
        metadata: row.metadata ? JSON.parse(row.metadata) : undefined
      });
    }
    
    return chunks;
  } catch (error) {
    console.error('Error getting document text chunks:', error);
    return [];
  }
};

/**
 * Store vector embeddings for text chunks
 * Note: In a real implementation, you would use a proper vector embedding model
 * This is a placeholder that stores random vectors as embeddings
 */
export const storeVectorEmbeddings = async (
  documentId: string,
  chunkId: string,
  embedding: number[],
  embeddingModel: string = 'mock-model'
): Promise<void> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    // Convert embedding array to Buffer
    const embeddingBuffer = Buffer.from(new Float32Array(embedding).buffer);
    
    // Store the embedding
    await database.executeSql(
      `INSERT OR REPLACE INTO vector_embeddings (
        id, chunk_id, document_id, embedding, embedding_model, created_at
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [
        `emb_${chunkId}`,
        chunkId,
        documentId,
        embeddingBuffer,
        embeddingModel,
        new Date().toISOString()
      ]
    );
  } catch (error) {
    console.error('Error storing vector embedding:', error);
    throw error;
  }
};

/**
 * Generate a mock embedding vector for text
 * In a real implementation, you would use a proper embedding model
 */
export const generateMockEmbedding = (text: string, dimensions: number = 384): number[] => {
  // Create a deterministic but simple "embedding" based on the text
  // This is just for demonstration purposes
  const embedding = new Array(dimensions).fill(0);
  
  // Use a simple hash of the text to seed the embedding
  let hash = 0;
  for (let i = 0; i < text.length; i++) {
    hash = ((hash << 5) - hash) + text.charCodeAt(i);
    hash |= 0; // Convert to 32bit integer
  }
  
  // Use the hash to generate a pseudo-random but deterministic embedding
  const random = (n: number) => {
    const x = Math.sin(n) * 10000;
    return x - Math.floor(x);
  };
  
  for (let i = 0; i < dimensions; i++) {
    embedding[i] = random(hash + i) * 2 - 1; // Values between -1 and 1
  }
  
  // Normalize the embedding
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
  return embedding.map(val => val / magnitude);
};

/**
 * Process document text and store embeddings
 */
export const processAndStoreDocumentVectors = async (
  documentId: string,
  chunks: TextChunk[]
): Promise<void> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    // Store the text chunks
    await storeDocumentTextChunks(documentId, chunks);
    
    // Generate and store embeddings for each chunk
    for (const chunk of chunks) {
      const embedding = generateMockEmbedding(chunk.text);
      await storeVectorEmbeddings(documentId, chunk.id, embedding);
    }
    
    console.log(`Processed and stored vectors for document ${documentId}`);
  } catch (error) {
    console.error('Error processing and storing document vectors:', error);
    throw error;
  }
};

/**
 * Search for similar text chunks using vector similarity
 * Note: This is a simplified implementation that doesn't use actual vector similarity
 * In a real implementation, you would use a proper vector similarity search
 */
export const searchSimilarChunks = async (
  query: string,
  limit: number = 5
): Promise<{ documentId: string; chunkId: string; text: string; score: number }[]> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    // In a real implementation, you would:
    // 1. Generate an embedding for the query
    // 2. Find the most similar embeddings in the database
    // 3. Return the corresponding text chunks
    
    // For now, we'll use a simple text search as a placeholder
    const [results] = await database.executeSql(
      `SELECT c.id as chunk_id, c.document_id, c.text, 
              (LENGTH(c.text) - LENGTH(REPLACE(LOWER(c.text), LOWER(?), ''))) / LENGTH(?) AS score
       FROM text_chunks c
       WHERE c.text LIKE ?
       ORDER BY score DESC
       LIMIT ?`,
      [query, query, `%${query}%`, limit]
    );
    
    const searchResults = [];
    for (let i = 0; i < results.rows.length; i++) {
      const row = results.rows.item(i);
      searchResults.push({
        documentId: row.document_id,
        chunkId: row.chunk_id,
        text: row.text,
        score: row.score
      });
    }
    
    return searchResults;
  } catch (error) {
    console.error('Error searching similar chunks:', error);
    return [];
  }
};

/**
 * Cache search results
 */
export const cacheSearchResults = async (
  query: string,
  results: any[],
  expirationMinutes: number = 60
): Promise<void> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    const now = new Date();
    const expiresAt = new Date(now.getTime() + expirationMinutes * 60000);
    
    await database.executeSql(
      `INSERT OR REPLACE INTO search_cache (
        id, query, results, created_at, expires_at
      ) VALUES (?, ?, ?, ?, ?)`,
      [
        `cache_${Date.now()}`,
        query,
        JSON.stringify(results),
        now.toISOString(),
        expiresAt.toISOString()
      ]
    );
  } catch (error) {
    console.error('Error caching search results:', error);
  }
};

/**
 * Get cached search results
 */
export const getCachedSearchResults = async (query: string): Promise<any[] | null> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    const now = new Date().toISOString();
    
    const [results] = await database.executeSql(
      `SELECT results FROM search_cache 
       WHERE query = ? AND expires_at > ?
       ORDER BY created_at DESC
       LIMIT 1`,
      [query, now]
    );
    
    if (results.rows.length > 0) {
      return JSON.parse(results.rows.item(0).results);
    }
    
    return null;
  } catch (error) {
    console.error('Error getting cached search results:', error);
    return null;
  }
};

/**
 * Clean expired cache entries
 */
export const cleanExpiredCache = async (): Promise<void> => {
  try {
    if (!database) {
      database = await initDatabase();
    }
    
    const now = new Date().toISOString();
    
    await database.executeSql(
      `DELETE FROM search_cache WHERE expires_at < ?`,
      [now]
    );
  } catch (error) {
    console.error('Error cleaning expired cache:', error);
  }
};
