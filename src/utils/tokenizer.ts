import RNFS from 'react-native-fs';
import { MODELS_DIR } from './onnxRuntime';
import { isModelDownloaded } from './modelManager';
import { Tokenizer } from 'tokenizers';

// Simple tokenizer interface
export interface SimpleTokenizer {
  encode: (text: string) => Promise<number[]>;
  decode: (tokens: number[]) => Promise<string>;
}

// Tokenizer instance
let tokenizer: TokenizerWrapper | null = null;

// Wrapper class for the tokenizers library
class Token<PERSON><PERSON>rapper implements SimpleTokenizer {
  private tokenizerInstance: Tokenizer;

  constructor(tokenizerInstance: Tokenizer) {
    this.tokenizerInstance = tokenizerInstance;
  }

  async encode(text: string): Promise<number[]> {
    const encoding = await this.tokenizerInstance.encode(text);
    return encoding.getIds();
  }

  async decode(tokens: number[]): Promise<string> {
    return await this.tokenizerInstance.decode(tokens, false);
  }
}

// Initialize tokenizer
export const initTokenizer = async (tokenizerPath?: string): Promise<SimpleTokenizer> => {
  try {
    if (tokenizerPath) {
      // Check if tokenizer file exists
      const exists = await RNFS.exists(tokenizerPath);
      if (!exists) {
        throw new Error(`Tokenizer file not found at path: ${tokenizerPath}`);
      }

      // Load tokenizer from file
      console.log(`Loading tokenizer from ${tokenizerPath}...`);
      const tokenizerInstance = await Tokenizer.fromFile(tokenizerPath);
      tokenizer = new TokenizerWrapper(tokenizerInstance);
      console.log('Tokenizer loaded successfully');
    } else {
      // Try to load the default tokenizer
      const defaultTokenizerPath = `${MODELS_DIR}/tinyllama-1.1b-chat-v1.0-tokenizer.json`;
      const exists = await RNFS.exists(defaultTokenizerPath);

      if (exists) {
        console.log(`Loading default tokenizer from ${defaultTokenizerPath}...`);
        const tokenizerInstance = await Tokenizer.fromFile(defaultTokenizerPath);
        tokenizer = new TokenizerWrapper(tokenizerInstance);
        console.log('Default tokenizer loaded successfully');
      } else {
        throw new Error('No tokenizer file available. Please download a tokenizer model first.');
      }
    }

    return tokenizer;
  } catch (error) {
    console.error('Error initializing tokenizer:', error);
    throw error;
  }
};

// Get tokenizer
export const getTokenizer = async (): Promise<SimpleTokenizer> => {
  if (tokenizer) {
    return tokenizer;
  }

  // Initialize simple tokenizer
  return initTokenizer();
};

// Encode text to token IDs
export const encodeText = async (text: string): Promise<number[]> => {
  try {
    const tokenizerInstance = await getTokenizer();
    return await tokenizerInstance.encode(text);
  } catch (error) {
    console.error('Error encoding text:', error);
    throw error;
  }
};

// Decode token IDs to text
export const decodeTokens = async (tokenIds: number[]): Promise<string> => {
  try {
    const tokenizerInstance = await getTokenizer();
    return await tokenizerInstance.decode(tokenIds);
  } catch (error) {
    console.error('Error decoding tokens:', error);
    throw error;
  }
};

// Count tokens in text
export const countTokens = async (text: string): Promise<number> => {
  try {
    const tokenIds = await encodeText(text);
    return tokenIds.length;
  } catch (error) {
    console.error('Error counting tokens:', error);
    throw error;
  }
};
