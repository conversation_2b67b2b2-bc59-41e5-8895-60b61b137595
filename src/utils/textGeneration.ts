import { encodeText, decodeTokens, setActiveModelId } from './customTokenizer';
import { Tensor } from 'onnxruntime-react-native';
import { loadOnnxModel, runInference, ModelInfo } from './onnxRuntime';
import { getModelMetadata, ensureModelDownloaded, loadModelWithConfig } from './modelManager';
import RNFS from 'react-native-fs';

// Model session and metadata
let activeModelInfo: ModelInfo | null = null; // Store the active model's info
// Initialize model
// No longer need separate initModel, session loading will happen within generateText

// Get model session
// Renamed and refactored to return ModelInfo or null for fallback
export const getActiveModelInfo = async (): Promise<ModelInfo | null> => {
  // If already determined, return it (optional optimization)
  // if (activeModelInfo) return activeModelInfo;

  // Get available models
  const models = await getModelMetadata();

  // First try to find a non-fallback model that's already downloaded
  // Prioritize downloaded, generative, non-fallback models
  let model = models.find(m => m.type === 'onnx' && m.modelType === 'generative' && m.isDownloaded && !m.isFallback);
  console.log(`Found model: ${model?.id}`);


  // If still no model, use a fallback model
  if (!model) {
    // If still no model, find the fallback model
    model = models.find(m => m.type === 'onnx' && m.isFallback);
    if (model) {
      console.log(`Using fallback model: ${model.id}`);
      activeModelInfo = model; // Store fallback info
      return model; // Return fallback model info (generateText will handle null session)
    } else {
      throw new Error('No generative or fallback model available. Please check model definitions.');
    }
  }

  // Ensure model is downloaded
  if (!model.isDownloaded) {
    console.log(`Model ${model.id} not downloaded, attempting to download...`);
    const downloaded = await ensureModelDownloaded(model.id, (progress) => {
      console.log(`Downloading model ${model.id}: ${Math.round(progress * 100)}%`);
    });

    if (!downloaded) {
      // If download fails, try to use a fallback model
      // If download fails, find and use the fallback model
      const fallbackModel = models.find(m => m.type === 'onnx' && m.isFallback);
      if (fallbackModel) {
        console.log(`Download failed for ${model.id}, using fallback model: ${fallbackModel.id}`);
        activeModelInfo = fallbackModel;
        return fallbackModel; // Return fallback model info
      } else {
        throw new Error(`Failed to download model ${model.id}. Please try again later.`);
      }
    }
  }

  // Model should be downloaded now, load its configuration
  console.log(`Loading model with configuration: ${model.id}`);
  const modelWithConfig = await loadModelWithConfig(model.id);

  if (!modelWithConfig) {
    console.warn(`Failed to load model configuration for ${model.id}, proceeding with basic model info`);
    activeModelInfo = model;
  } else {
    console.log(`Successfully loaded model with configuration: ${model.id}`);
    activeModelInfo = modelWithConfig;
  }

  // Set the active model ID for tokenization
  setActiveModelId(model.id);
  return activeModelInfo; // Return the model info with configuration if available
};

// Generation parameters
export interface GenerationParams {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  repetitionPenalty?: number;
  stopSequences?: string[];
}

// Special tokens to filter out from generation
export const SPECIAL_TOKENS = [
  '<s>',      // Start of sequence
  '</s>',     // End of sequence
  '<pad>',    // Padding token
  '<|im_start|>', // Instruction start
  '<|im_end|>',   // Instruction end
  '<|endoftext|>', // End of text
  '<unk>',    // Unknown token
];

// Maximum context window size
// This is a default value, but we'll get the actual value from the model metadata
export const MAX_CONTEXT_WINDOW = 1024;

// Get current model's context window size
// This function is less reliable now, get size from activeModelInfo directly
// export const getCurrentContextWindowSize = (): number => {
//   return activeModelInfo?.contextWindowSize || MAX_CONTEXT_WINDOW;
// };

// Default generation parameters
export const DEFAULT_GENERATION_PARAMS: GenerationParams = {
  maxTokens: 100, // Reduced to avoid exceeding context window
  temperature: 0.7,
  topP: 0.9,
  repetitionPenalty: 1.1,
  stopSequences: ['</s>', '<|im_end|>', '<|endoftext|>'],
};

// Generate text
export const generateText = async (
  prompt: string,
  params: GenerationParams = DEFAULT_GENERATION_PARAMS,
  onToken?: (token: string) => void
): Promise<string> => {
  try {
    // Get active model info
    const modelInfo = await getActiveModelInfo();

    // If no model info (error) or it's the fallback, use fallback generation
    if (!modelInfo || modelInfo.isFallback) {
      console.log(`Using fallback implementation (Model: ${modelInfo?.id ?? 'None'})`);
      return await generateFallbackResponse(prompt, onToken);
    }

    // Check if the selected model is generative
    if (modelInfo.modelType !== 'generative' && modelInfo.modelType !== undefined) {
        console.warn(`Model ${modelInfo.id} is type '${modelInfo.modelType}', not 'generative'. Using fallback.`);
        return await generateFallbackResponse(prompt, onToken);
    }

    // We'll load the session and use it immediately for each inference step
    // This is important because on some platforms (like MSVC 2019 64-bit),
    // model initialization and usage must happen together

    // Store model information for use during inference
    let modelPath: string;
    let modelId: string;
    let modelConfig: any;

    try {
        // Fix path with double slashes
        modelPath = modelInfo.path.replace(/\/\//g, '/');
        modelId = modelInfo.id;
        modelConfig = modelInfo.modelConfig;

        console.log(`Preparing to load model: ${modelId} from ${modelPath}`);

        // Check if the file exists
        const exists = await RNFS.exists(modelPath);
        if (!exists) {
            console.error(`Model file not found at ${modelPath}`);
            throw new Error(`Model file not found at ${modelPath}`);
        }

        // We'll load the session later, right before inference
        // This ensures initialization and usage happen together
        console.log(`Model file exists for ${modelId}, will load at inference time`);
    } catch (loadError: any) {
        console.error(`Failed to load session for ${modelInfo.id}:`, loadError);

        // Check for specific error types
        const errorMessage = loadError?.message || '';
        if (errorMessage.includes('InferenceSession') || errorMessage.includes("doesn't exist")) {
            console.error('ONNX runtime module not properly loaded. This could be due to a missing or incompatible native module.');
        } else if (errorMessage.includes('No such file') || errorMessage.includes('not found')) {
            console.error(`Model file not found or inaccessible. Path: ${modelInfo.path}`);
        } else if (errorMessage.includes('ORT_INVALID_PROTOBUF') || errorMessage.includes('Protobuf parsing failed')) {
            console.error('Invalid ONNX model format. The model file may be corrupted or incompatible.');
        }

        console.log('Falling back to mock implementation due to session load error.');
        return await generateFallbackResponse(prompt, onToken);
    }

    try {
      // Encode the prompt using the model-specific tokenizer
      let inputTokenIds = await encodeText(prompt, modelInfo.id);
      let currentText = '';

      // Use model-specific context window size
      const contextWindowSize = modelInfo.contextWindowSize || MAX_CONTEXT_WINDOW; // Use model's or default
      const maxInputLength = contextWindowSize - (params.maxTokens || DEFAULT_GENERATION_PARAMS.maxTokens!) - 20; // Leave room for generation + buffer
      if (inputTokenIds.length > maxInputLength) {
        console.warn(`Input too long (${inputTokenIds.length} tokens), truncating to ${maxInputLength} tokens`);
        // Keep the beginning and end of the prompt, removing tokens from the middle
        // This preserves important context at the beginning and the most recent content
        const beginLength = Math.floor(maxInputLength * 0.3); // Keep 30% from beginning
        const endLength = maxInputLength - beginLength; // Keep 70% from end

        const beginTokens = inputTokenIds.slice(0, beginLength);
        const endTokens = inputTokenIds.slice(-endLength);
        inputTokenIds = [...beginTokens, ...endTokens];

        console.log(`Truncated input from ${inputTokenIds.length} to ${inputTokenIds.length} tokens by keeping beginning and end`);
        currentText = '... '; // Add ellipsis to indicate truncation
      }

      // Prepare for generation
      let generatedTokenIds: number[] = [];
      let allTokenIds = [...inputTokenIds];

      // Set up generation parameters
      const maxTokens = params.maxTokens !== undefined ? params.maxTokens : DEFAULT_GENERATION_PARAMS.maxTokens!;
      const temperature = params.temperature !== undefined ? params.temperature : DEFAULT_GENERATION_PARAMS.temperature!;
      const topP = params.topP !== undefined ? params.topP : DEFAULT_GENERATION_PARAMS.topP!;
      const stopSequences = params.stopSequences !== undefined ? params.stopSequences : DEFAULT_GENERATION_PARAMS.stopSequences!;

      // Store past key-value states for efficient generation
      let pastKeyValues: Record<string, any> = {};

      // Generate tokens one by one
      for (let i = 0; i < maxTokens; i++) {
        // Check if we're approaching the context window limit
        const currentContextWindowSize = modelInfo.contextWindowSize || MAX_CONTEXT_WINDOW;
        const safeLimit = Math.max(currentContextWindowSize - 50, 1024); // Leave some margin, ensure minimum
        if (allTokenIds.length >= safeLimit) {
          console.warn(`Approaching context window limit (${allTokenIds.length}/${currentContextWindowSize}), stopping generation`);
          break;
        }

        // For each inference step, we'll load a fresh session and use it immediately
        // This ensures model initialization and usage happen together as recommended
        let session;
        let sessionMetadata;
        let outputs;

        try {
          // Load a fresh session for this inference step
          console.log(`Loading fresh session for inference step ${i} with model ${modelId}`);
          const result = await loadOnnxModel(modelPath, modelId, modelConfig);
          session = result.session;
          sessionMetadata = result.metadata;

          // Update model info with dynamically extracted metadata if available (first iteration only)
          if (i === 0) {
            if (sessionMetadata && sessionMetadata.inputNames?.length > 0) {
              console.log(`Using dynamically extracted input names: ${sessionMetadata.inputNames.join(', ')}`);
              modelInfo.inputNames = sessionMetadata.inputNames;
            }
            if (sessionMetadata && sessionMetadata.outputNames?.length > 0) {
              console.log(`Using dynamically extracted output names: ${sessionMetadata.outputNames.join(', ')}`);
              modelInfo.outputNames = sessionMetadata.outputNames;
            }
          }

          // --- Dynamically create input tensors based on modelInfo.inputNames ---
          const inputs: Record<string, Tensor> = {};
          const requiredInputs = modelInfo.inputNames || ['input_ids', 'attention_mask']; // Default if not specified

          // For the first iteration, use the full input sequence
          // For subsequent iterations, only use the last generated token
          const inputSequence = i === 0 || Object.keys(pastKeyValues).length === 0
              ? allTokenIds
              : [allTokenIds[allTokenIds.length - 1]];
          const seqLength = inputSequence.length;

          if (requiredInputs.includes('input_ids')) {
              inputs['input_ids'] = new Tensor(
                  'int64',
                  new BigInt64Array(inputSequence.map(id => BigInt(id))),
                  [1, seqLength]
              );
          }

          if (requiredInputs.includes('attention_mask')) {
              // For the first iteration, use a mask of all 1s
              // For subsequent iterations with past key-values, just use a single 1 for the new token
              const maskLength = i === 0 || Object.keys(pastKeyValues).length === 0
                  ? seqLength
                  : allTokenIds.length;
              inputs['attention_mask'] = new Tensor(
                  'int64',
                  new BigInt64Array(maskLength).fill(BigInt(1)),
                  [1, maskLength]
              );
          }

          if (requiredInputs.includes('token_type_ids')) {
              // Required by models like BERT/DistilBERT, usually 0s for single sequence
              inputs['token_type_ids'] = new Tensor(
                  'int64',
                  new BigInt64Array(allTokenIds.length).fill(BigInt(0)),
                  [1, allTokenIds.length]
              );
          }

          if (requiredInputs.includes('position_ids')) {
              // For the first iteration, use sequential positions
              // For subsequent iterations with past key-values, use the position after the last token
              let positions;
              if (i === 0 || Object.keys(pastKeyValues).length === 0) {
                  positions = Array.from({ length: seqLength }, (_, k) => BigInt(k));
              } else {
                  // Use the position of the last token
                  positions = [BigInt(allTokenIds.length - 1)];
              }

              inputs['position_ids'] = new Tensor(
                  'int64',
                  new BigInt64Array(positions),
                  [1, positions.length]
              );
          }

          // Add past key-value states for subsequent iterations
          if (i > 0 && Object.keys(pastKeyValues).length > 0) {
              // Map 'present.X.key/value' to 'past_key_values.X.key/value'
              Object.keys(pastKeyValues).forEach(key => {
                  // Convert 'present.X.key' to 'past_key_values.X.key'
                  const pastKey = key.replace('present', 'past_key_values');
                  inputs[pastKey] = pastKeyValues[key];
              });
          }

          // Check if all required inputs were created
          const createdInputs = Object.keys(inputs);
          const missingInputs = requiredInputs.filter((name: string) =>
              !createdInputs.includes(name) && !name.startsWith('past_key_values')
          );

          if (missingInputs.length > 0) {
              console.error(`Cannot run inference: Missing required inputs for model ${modelInfo.id}: ${missingInputs.join(', ')}`);
              // Fallback or throw error
              return await generateFallbackResponse(prompt, onToken);
          }

          // Log the inputs for debugging
          console.log(`Running inference for ${modelInfo.id} with ${allTokenIds.length} tokens and inputs: ${Object.keys(inputs).join(', ')}`);

          // Run inference with the freshly loaded session
          // Pass the iteration count as the retry count to help with tensor creation
          outputs = await runInference(session, inputs, i, 2);
        } catch (inferenceError: any) {
          console.error('Error during inference:', inferenceError);

          // Check if the error is related to context window size or tensor shapes
          const errorMessage = inferenceError?.message || '';

          // Handle model-agnostic errors instead of model-specific errors
          // Handle reshape errors
          if (errorMessage.includes('Reshape') &&
              errorMessage.includes('input_shape_size == size was false')) {
            console.warn('Detected reshape error. This is likely due to a mismatch between attention heads and key-value heads.');

            // If we've generated some tokens already, return what we have
            if (currentText.length > 0) {
              return currentText + '... (truncated due to model architecture limitation)';
            }

            // Fall back to mock implementation
            return await generateFallbackResponse(prompt, onToken);
          }

          // Handle MatMul dimension mismatch errors
          if (errorMessage.includes('MatMul') &&
              errorMessage.includes('dimension mismatch')) {
            console.warn('Detected MatMul dimension mismatch. This is likely due to tensor shape incompatibility.');

            // Extract the layer information if available
            const layerMatch = errorMessage.match(/layers\.(\d+)\/self_attn/);
            if (layerMatch) {
              console.warn(`The issue is in layer ${layerMatch[1]} of the model's self-attention mechanism.`);
            }

            // If we've generated some tokens already, return what we have
            if (currentText.length > 0) {
              return currentText + '... (truncated due to tensor dimension mismatch)';
            }

            // Try again with a smaller prompt if this is the first token
            if (prompt.length > 100) {
              console.log('Trying again with a shorter prompt...');
              return await generateText(
                prompt.slice(0, Math.floor(prompt.length * 0.5)), // Use only 50% of the prompt
                { ...params, maxTokens: Math.min(params.maxTokens || 50, 50) }, // Limit max tokens
                onToken
              );
            }

            // Fall back to mock implementation for very short prompts
            return await generateFallbackResponse(prompt, onToken);
          }

          // Handle invalid rank errors
          if (errorMessage.includes('Invalid rank for input') ||
              errorMessage.includes('Got: 5 Expected: 4')) {
            console.warn('Detected tensor rank error. The model expects 4D tensors but received tensors with incorrect dimensions.');

            // If we've generated some tokens already, return what we have
            if (currentText.length > 0) {
              return currentText + '... (truncated due to tensor rank mismatch)';
            }

            // Otherwise fall back to mock implementation
            return await generateFallbackResponse(prompt, onToken);
          }

          // Check for other dimension/shape errors
          if (errorMessage.includes('broadcast') ||
              errorMessage.includes('dimension') ||
              errorMessage.includes('shape') ||
              errorMessage.includes('size') ||
              errorMessage.includes('rank')) {
            console.warn('Detected possible context window or tensor shape limitation, truncating sequence');

            // If we've generated some tokens already, return what we have
            if (currentText.length > 0) {
              return currentText + '... (truncated due to context limit)';
            }

            // Try again with a smaller context window
            return await generateText(
              prompt.slice(0, Math.floor(prompt.length * 0.7)), // Use only 70% of the prompt
              { ...params, maxTokens: Math.floor(params.maxTokens! * 0.7) }, // Reduce max tokens
              onToken
            );
          }

          // For other errors, if we've generated some tokens already, return what we have
          if (currentText.length > 0) {
            return currentText;
          }

          // Otherwise fall back to mock implementation
          return await generateFallbackResponse(prompt, onToken);
        }

        // Get logits from output - use the name specified in modelInfo or dynamically extracted
        const outputName = modelInfo.outputNames?.[0] || sessionMetadata?.outputNames?.[0] || 'logits'; // Default to 'logits'
        console.log(`Looking for output tensor with name: ${outputName}`);
        const outputTensor = outputs[outputName];

        // Store the key-value cache outputs for the next inference step
        const presentKeyValues: Record<string, any> = {};
        Object.keys(outputs).forEach(key => {
          if (key.startsWith('present.')) {
            presentKeyValues[key] = outputs[key];
          }
        });

        if (!outputTensor) {
             console.error(`Output tensor '${outputName}' not found in model outputs for ${modelInfo.id}. Available outputs: ${Object.keys(outputs).join(', ')}`);
             // Try to find any output that might contain logits
             const availableOutputs = Object.keys(outputs);
             const possibleLogitsOutput = availableOutputs.find(name =>
                 name.includes('logit') || name.includes('output') || name.includes('last')
             );

             if (possibleLogitsOutput) {
                 console.log(`Trying alternative output tensor: ${possibleLogitsOutput}`);
                 const logits = outputs[possibleLogitsOutput];
                 if (logits) {
                     console.log(`Using alternative output tensor: ${possibleLogitsOutput}`);
                     // Process the alternative output tensor
                     const lastTokenLogits = logits.data.slice(
                       -logits.dims[2],
                       logits.data.length
                     );

                     // Sample the next token
                     const nextTokenId = sampleNextToken(
                       lastTokenLogits as Float32Array,
                       temperature,
                       topP
                     );

                     // Add the new token to generated tokens
                     generatedTokenIds.push(nextTokenId);
                     allTokenIds.push(nextTokenId);

                     // Decode the new token
                     const newToken = await decodeTokens([nextTokenId], modelInfo.id);
                     currentText += newToken;

                     // Call the token callback if provided
                     if (onToken) {
                       onToken(newToken);
                     }

                     return currentText;
                 }
             }

             return await generateFallbackResponse(prompt, onToken);
        }

        const logits = outputTensor; // Use the dynamically retrieved output tensor

        // Get the last token's logits
        const lastTokenLogits = logits.data.slice(
          -logits.dims[2],
          logits.data.length
        );

        // Sample the next token
        const nextTokenId = sampleNextToken(
          lastTokenLogits as Float32Array,
          temperature,
          topP
        );

        // Add the new token to generated tokens
        generatedTokenIds.push(nextTokenId);
        allTokenIds.push(nextTokenId);

        // Decode the new token
        const newToken = await decodeTokens([nextTokenId]);

        // Check if it's a special token
        const isSpecialToken = SPECIAL_TOKENS.includes(newToken.trim());

        if (isSpecialToken) {
          console.log(`Skipping special token: ${newToken}`);
          // We don't increment the token count for special tokens
          i--;
        } else {
          // Process regular token
          currentText += newToken;

          // Call the token callback if provided
          if (onToken) {
            onToken(newToken);
          }

          // Update the past key-value states for the next iteration
          pastKeyValues = {};
          Object.keys(outputs).forEach(key => {
            if (key.startsWith('present.')) {
              pastKeyValues[key] = outputs[key];
            }
          });

          // Check for stop sequences
          let shouldBreakMainLoop = false;
          if (stopSequences.some(seq => currentText.includes(seq))) {
            // Remove the stop sequence from the end
            for (const seq of stopSequences) {
              if (currentText.endsWith(seq)) {
                currentText = currentText.slice(0, -seq.length);
                break;
              }
            }
            shouldBreakMainLoop = true;
          }

          // If we found a stop sequence, exit the main loop
          if (shouldBreakMainLoop) {
            break;
          }
        }
      }

      return currentText;
    } catch (modelError) {
      console.error('Error with model, falling back to mock implementation:', modelError);
      return await generateFallbackResponse(prompt, onToken);
    }
  } catch (error) {
    console.error('Error generating text:', error);
    throw error;
  }
};

// Generate a fallback response for when the model is not available
const generateFallbackResponse = async (
  prompt: string,
  onToken?: (token: string) => void
): Promise<string> => {
  // Extract relevant keywords from the prompt
  const keywords = extractKeywords(prompt);

  // Create a more natural-sounding response
  let response = "";

  // Different response templates for variety
  const responseTemplates = [
    "Based on the documents you've provided, I found some relevant information:",
    "After analyzing your documents, here's what I found:",
    "I've searched through your documents and found these relevant details:",
    "Here's what I discovered in your documents regarding your query:"
  ];

  // Select a random template
  const templateIndex = Math.floor(Math.random() * responseTemplates.length);
  response = responseTemplates[templateIndex] + "\n\n";

  if (keywords.length > 0) {
    // Add some relevant content based on keywords
    keywords.forEach((keyword, index) => {
      // Different formats for each point to make it seem more natural
      if (index === 0) {
        response += `[${index + 1}] I found that the documents discuss "${keyword}" in detail.\n`;
      } else if (index === 1) {
        response += `[${index + 1}] There's also information about how "${keyword}" relates to the main topic.\n`;
      } else {
        response += `[${index + 1}] Additionally, "${keyword}" is mentioned as an important aspect.\n`;
      }
    });

    // Add a conclusion
    response += "\nThese points should help address your query. Let me know if you need more specific information.";
  } else {
    // Generic response if no keywords found
    response += "[1] The documents contain general information that appears to be relevant to your query.\n";
    response += "[2] There are several sections that discuss topics that might interest you.\n";
    response += "\nIf you could provide more specific details about what you're looking for, I can give you more targeted information.";
  }

  // Simulate streaming tokens if callback is provided
  if (onToken) {
    // Split by characters to make it look more like token-by-token generation
    const chunks = response.split(/(?<=\s)/g); // Split after spaces
    for (const chunk of chunks) {
      onToken(chunk);
      // Randomize the delay slightly to make it look more natural
      const delay = 30 + Math.random() * 40; // 30-70ms
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return response;
};

// Extract keywords from a prompt
const extractKeywords = (prompt: string): string[] => {
  // Simple keyword extraction - remove common words and punctuation
  const commonWords = ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'about', 'is', 'are', 'was', 'were'];

  // Clean and tokenize the prompt
  const words = prompt.toLowerCase()
    .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3 && !commonWords.includes(word));

  // Remove duplicates and limit to 5 keywords
  return [...new Set(words)].slice(0, 5);
};

// Sample the next token using temperature and top-p sampling
const sampleNextToken = (
  logits: Float32Array,
  temperature: number,
  topP: number
): number => {
  // Apply temperature
  const scaledLogits = new Float32Array(logits.length);
  for (let i = 0; i < logits.length; i++) {
    scaledLogits[i] = logits[i] / temperature;
  }

  // Convert to probabilities using softmax
  const probs = softmax(scaledLogits);

  // Apply top-p (nucleus) sampling
  const sortedIndices = Array.from({ length: probs.length }, (_, i) => i)
    .sort((a, b) => probs[b] - probs[a]);

  let cumsum = 0;
  const topPIndices: number[] = [];

  for (const idx of sortedIndices) {
    if (cumsum >= topP) break;
    topPIndices.push(idx);
    cumsum += probs[idx];
  }

  // Sample from the filtered distribution
  const filteredProbs = new Float32Array(topPIndices.length);
  for (let i = 0; i < topPIndices.length; i++) {
    filteredProbs[i] = probs[topPIndices[i]];
  }

  // Normalize the filtered probabilities
  const sum = filteredProbs.reduce((a, b) => a + b, 0);
  for (let i = 0; i < filteredProbs.length; i++) {
    filteredProbs[i] /= sum;
  }

  // Sample from the normalized distribution
  const random = Math.random();
  let cdf = 0;

  for (let i = 0; i < filteredProbs.length; i++) {
    cdf += filteredProbs[i];
    if (random < cdf) {
      return topPIndices[i];
    }
  }

  // Fallback to the most likely token
  return topPIndices[0];
};

// Softmax function
const softmax = (logits: Float32Array): Float32Array => {
  const maxLogit = Math.max(...Array.from(logits));
  const exps = new Float32Array(logits.length);
  let sumExp = 0;

  for (let i = 0; i < logits.length; i++) {
    exps[i] = Math.exp(logits[i] - maxLogit);
    sumExp += exps[i];
  }

  const probs = new Float32Array(logits.length);
  for (let i = 0; i < logits.length; i++) {
    probs[i] = exps[i] / sumExp;
  }

  return probs;
};

// Context window management
export interface ContextWindow {
  maxTokens: number;
  reservedTokens: number;
  systemPrompt: string;
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
}

// Create context window
export const createContextWindow = (
  systemPrompt: string,
  maxTokens?: number,
  reservedTokens: number = 100
): ContextWindow => {
  // Use the current model's context window size if maxTokens is not provided
  const contextSize = maxTokens || (activeModelInfo?.contextWindowSize || MAX_CONTEXT_WINDOW);

  return {
    maxTokens: contextSize,
    reservedTokens,
    systemPrompt,
    messages: [
      {
        role: 'system',
        content: systemPrompt,
      },
    ],
  };
};

// Add message to context window
export const addMessageToContext = async (
  context: ContextWindow,
  role: 'user' | 'assistant',
  content: string
): Promise<ContextWindow> => {
  // Add message
  const newContext = {
    ...context,
    messages: [
      ...context.messages,
      { role, content },
    ],
  };

  // Check if we need to trim the context
  const totalTokens = await countContextTokens(newContext);

  if (totalTokens > context.maxTokens - context.reservedTokens) {
    // Trim context
    return trimContext(newContext);
  }

  return newContext;
};

// Count tokens in context
export const countContextTokens = async (context: ContextWindow): Promise<number> => {
  try {
    // Convert context to prompt
    const prompt = contextToPrompt(context);

    // Count tokens
    const tokenIds = await encodeText(prompt);
    return tokenIds.length;
  } catch (error) {
    console.error('Error counting context tokens:', error);
    return 0;
  }
};

// Trim context to fit within token limit
export const trimContext = async (context: ContextWindow): Promise<ContextWindow> => {
  // If we only have the system message, we can't trim further
  if (context.messages.length <= 1) {
    return context;
  }

  // Create a new context with just the system message
  const newContext: ContextWindow = {
    ...context,
    messages: [context.messages[0]],
  };

  // Add messages from newest to oldest until we hit the token limit
  for (let i = context.messages.length - 1; i >= 1; i--) {
    const tempContext = {
      ...newContext,
      messages: [
        ...newContext.messages,
        context.messages[i],
      ],
    };

    const totalTokens = await countContextTokens(tempContext);

    if (totalTokens <= context.maxTokens - context.reservedTokens) {
      newContext.messages.push(context.messages[i]);
    } else {
      break;
    }
  }

  // Sort messages back into chronological order
  newContext.messages.sort((a, b) => {
    if (a.role === 'system') return -1;
    if (b.role === 'system') return 1;
    return 0;
  });

  return newContext;
};

// Convert context to prompt
export const contextToPrompt = (context: ContextWindow): string => {
  let prompt = '';

  for (const message of context.messages) {
    if (message.role === 'system') {
      prompt += `<|im_start|>system\n${message.content}<|im_end|>\n`;
    } else if (message.role === 'user') {
      prompt += `<|im_start|>user\n${message.content}<|im_end|>\n`;
    } else if (message.role === 'assistant') {
      prompt += `<|im_start|>assistant\n${message.content}<|im_end|>\n`;
    }
  }

  // Add the assistant prefix for the response
  prompt += `<|im_start|>assistant\n`;

  return prompt;
};
